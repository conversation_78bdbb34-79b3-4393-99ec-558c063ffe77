package com.assessment.controller;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.assessment.dto.ApiResponse;
import com.assessment.entity.multitenant.TenantAssessmentRecord;
import com.assessment.repository.multitenant.TenantAssessmentRecordRepository;
import com.assessment.repository.multitenant.TenantRepository;
import com.assessment.repository.multitenant.PlatformUserRepository;
import com.assessment.repository.multitenant.GlobalScaleRegistryRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 系统评估控制器基础单元测试
 * 专注于测试控制器的核心逻辑
 */
@DisplayName("系统评估控制器基础单元测试")
class SystemAssessmentControllerBasicTest {

    private SystemAssessmentController systemAssessmentController;
    private TenantAssessmentRecordRepository mockAssessmentRepository;
    private TenantRepository mockTenantRepository;
    private PlatformUserRepository mockUserRepository;
    private GlobalScaleRegistryRepository mockScaleRepository;

    @BeforeEach
    void setUp() {
        mockAssessmentRepository = mock(TenantAssessmentRecordRepository.class);
        mockTenantRepository = mock(TenantRepository.class);
        mockUserRepository = mock(PlatformUserRepository.class);
        mockScaleRepository = mock(GlobalScaleRegistryRepository.class);
        
        systemAssessmentController = new SystemAssessmentController(
            mockAssessmentRepository,
            mockTenantRepository,
            mockScaleRepository,
            mockUserRepository
        );
    }

    @Test
    @DisplayName("测试获取评估记录列表 - 基本功能")
    void testGetAssessments_Basic() {
        // Arrange
        List<TenantAssessmentRecord> assessments = Arrays.asList(
            createSimpleMockAssessment("assessment1", "老年人评估1"),
            createSimpleMockAssessment("assessment2", "老年人评估2")
        );
        Page<TenantAssessmentRecord> assessmentsPage = new PageImpl<>(assessments);

        when(mockAssessmentRepository.findAll(any(Pageable.class)))
            .thenReturn(assessmentsPage);

        // Act
        ResponseEntity<ApiResponse<Map<String, Object>>> response = 
            systemAssessmentController.getAssessments(0, 20, null, null, null, null, null, null, null, null);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isTrue();
        
        Map<String, Object> data = response.getBody().getData();
        assertThat(data).containsKey("content");
        assertThat(data).containsKey("totalElements");
        assertThat(data.get("totalElements")).isEqualTo(2L);
    }

    @Test
    @DisplayName("测试获取评估记录列表 - 按状态筛选")
    void testGetAssessments_FilterByStatus() {
        // Arrange
        List<TenantAssessmentRecord> assessments = Arrays.asList(
            createSimpleMockAssessment("assessment1", "已完成评估")
        );
        Page<TenantAssessmentRecord> assessmentsPage = new PageImpl<>(assessments);

        when(mockAssessmentRepository.findAll(any(Pageable.class)))
            .thenReturn(assessmentsPage);

        // Act
        ResponseEntity<ApiResponse<Map<String, Object>>> response = 
            systemAssessmentController.getAssessments(0, 20, null, "APPROVED", null, null, null, null, null, null);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isTrue();
        
        Map<String, Object> data = response.getBody().getData();
        assertThat(data.get("totalElements")).isEqualTo(1L);
    }

    @Test
    @DisplayName("测试获取评估详情 - 成功")
    void testGetAssessment_Success() {
        // Arrange
        String assessmentId = UUID.randomUUID().toString();
        TenantAssessmentRecord assessment = createSimpleMockAssessment("test-assessment", "测试评估");
        assessment.setId(assessmentId);

        when(mockAssessmentRepository.findById(assessmentId))
            .thenReturn(Optional.of(assessment));

        // Act
        ResponseEntity<ApiResponse<SystemAssessmentController.AssessmentRecordWithDetails>> response = 
            systemAssessmentController.getAssessment(assessmentId);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isTrue();
        assertThat(response.getBody().getData()).isNotNull();
    }

    @Test
    @DisplayName("测试获取评估详情 - 记录不存在")
    void testGetAssessment_NotFound() {
        // Arrange
        String assessmentId = UUID.randomUUID().toString();

        when(mockAssessmentRepository.findById(assessmentId))
            .thenReturn(Optional.empty());

        // Act
        ResponseEntity<ApiResponse<SystemAssessmentController.AssessmentRecordWithDetails>> response = 
            systemAssessmentController.getAssessment(assessmentId);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.NOT_FOUND);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isFalse();
        assertThat(response.getBody().getMessage()).contains("评估记录不存在");
    }

    @Test
    @DisplayName("测试删除评估记录 - 成功")
    void testDeleteAssessment_Success() {
        // Arrange
        String assessmentId = UUID.randomUUID().toString();
        TenantAssessmentRecord assessment = createSimpleMockAssessment("test-assessment", "测试评估");
        assessment.setId(assessmentId);

        when(mockAssessmentRepository.findById(assessmentId))
            .thenReturn(Optional.of(assessment));

        // Act
        ResponseEntity<ApiResponse<String>> response = 
            systemAssessmentController.deleteAssessment(assessmentId);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isTrue();
        assertThat(response.getBody().getMessage()).isEqualTo("评估记录删除成功");
    }

    @Test
    @DisplayName("测试获取评估统计 - 成功")
    void testGetAssessmentStats_Success() {
        // Arrange
        when(mockAssessmentRepository.count()).thenReturn(1000L);
        when(mockAssessmentRepository.countByStatus("APPROVED")).thenReturn(800L);
        when(mockAssessmentRepository.countByStatus("SUBMITTED")).thenReturn(150L);
        when(mockAssessmentRepository.countByStatus("DRAFT")).thenReturn(50L);

        // Act
        ResponseEntity<ApiResponse<Map<String, Object>>> response = 
            systemAssessmentController.getAssessmentStats(null, null, null);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isTrue();
        
        Map<String, Object> data = response.getBody().getData();
        assertThat(data).containsKey("totalAssessments");
        assertThat(data.get("totalAssessments")).isEqualTo(1000L);
    }

    @Test
    @DisplayName("测试批量审核评估记录 - 成功")
    void testBatchReviewAssessments_Success() {
        // Arrange
        SystemAssessmentController.BatchReviewRequest request = 
            new SystemAssessmentController.BatchReviewRequest();
        request.setRecordIds(Arrays.asList(
            UUID.randomUUID().toString(),
            UUID.randomUUID().toString()
        ));
        request.setApproved(true);
        request.setReviewNotes("批量审核通过");

        for (String assessmentId : request.getRecordIds()) {
            TenantAssessmentRecord assessment = createSimpleMockAssessment("assessment-" + assessmentId, "测试评估");
            assessment.setId(assessmentId);
            when(mockAssessmentRepository.findById(assessmentId))
                .thenReturn(Optional.of(assessment));
            when(mockAssessmentRepository.save(any(TenantAssessmentRecord.class)))
                .thenReturn(assessment);
        }

        // Act
        ResponseEntity<ApiResponse<String>> response = 
            systemAssessmentController.batchReviewAssessments(request);

        // Assert
        assertThat(response).isNotNull();
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().isSuccess()).isTrue();
        assertThat(response.getBody().getMessage()).contains("批量审核完成");
    }

    /**
     * 创建简单的模拟评估记录对象
     */
    private TenantAssessmentRecord createSimpleMockAssessment(String recordNumber, String description) {
        TenantAssessmentRecord assessment = new TenantAssessmentRecord();
        assessment.setId(UUID.randomUUID().toString());
        assessment.setTenantId(UUID.randomUUID().toString());
        assessment.setRecordNumber(recordNumber);
        assessment.setSubjectId(UUID.randomUUID().toString());
        assessment.setScaleId(UUID.randomUUID().toString());
        assessment.setAssessorId(UUID.randomUUID().toString());
        assessment.setAssessmentDate(LocalDateTime.now());
        assessment.setScaleType(TenantAssessmentRecord.ScaleType.GLOBAL);
        assessment.setAssessmentType(TenantAssessmentRecord.AssessmentType.REGULAR);
        assessment.setStatus(TenantAssessmentRecord.RecordStatus.APPROVED);
        assessment.setWorkflowStage("completed");
        assessment.setCreatedAt(LocalDateTime.now());
        assessment.setUpdatedAt(LocalDateTime.now());
        assessment.setCreatedBy("test-user");
        assessment.setUpdatedBy("test-user");
        return assessment;
    }
}