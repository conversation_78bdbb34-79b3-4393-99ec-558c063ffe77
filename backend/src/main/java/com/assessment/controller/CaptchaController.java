package com.assessment.controller;

import com.assessment.service.SimpleCaptchaService;
import com.assessment.dto.CaptchaVerifyRequest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 滑动验证码控制器
 * 自研滑动拼图验证码功能
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-23
 */
@RestController
@RequestMapping("/api/captcha")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "验证码接口", description = "滑动验证码相关接口")
public class CaptchaController {

    private final SimpleCaptchaService captchaService;

    /**
     * 获取验证码
     * 生成滑动拼图验证码的底图和滑块
     */
    @GetMapping("/get")
    @Operation(summary = "获取验证码", description = "生成滑动拼图验证码")
    public ResponseEntity<Map<String, Object>> getCaptcha() {
        var response = captchaService.generateCaptcha();
        return ResponseEntity.ok(Map.of(
            "success", response.isSuccess(),
            "data", response.getData(),
            "message", response.getMessage()
        ));
    }

    /**
     * 校验验证码
     * 验证用户滑动操作是否正确
     */
    @PostMapping("/check")
    @Operation(summary = "校验验证码", description = "验证滑动拼图操作是否正确")
    public ResponseEntity<Map<String, Object>> checkCaptcha(@RequestBody CaptchaVerifyRequest request) {
        var response = captchaService.checkCaptcha(request.getToken(), request.getPointJson(), request.getVerification());
        return ResponseEntity.ok(Map.of(
            "success", response.isSuccess(),
            "data", response.getData(),
            "message", response.getMessage()
        ));
    }

    /**
     * 二次验证
     * 用于登录时的二次验证，确保验证码有效且未被重复使用
     */
    @PostMapping("/verify")
    @Operation(summary = "二次验证", description = "登录时进行二次验证")
    public ResponseEntity<Map<String, Object>> verifyCaptcha(@RequestBody CaptchaVerifyRequest request) {
        // 对于简单实现，二次验证和校验是相同的逻辑
        var response = captchaService.checkCaptcha(request.getToken(), request.getPointJson(), request.getVerification());
        return ResponseEntity.ok(Map.of(
            "success", response.isSuccess(),
            "data", response.getData(),
            "message", response.getMessage()
        ));
    }
}