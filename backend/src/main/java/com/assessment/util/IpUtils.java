package com.assessment.util;

import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;

/**
 * IP地址获取工具类
 * 支持从HTTP请求中获取真实客户端IP地址
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-24
 */
@Slf4j
public class IpUtils {

    private static final String UNKNOWN = "unknown";
    private static final String LOCALHOST_IPV4 = "127.0.0.1";
    private static final String LOCALHOST_IPV6 = "0:0:0:0:0:0:0:1";

    /**
     * 获取客户端真实IP地址
     * 支持代理、负载均衡等场景
     */
    public static String getClientIp(HttpServletRequest request) {
        if (request == null) {
            log.warn("HttpServletRequest为空，返回默认IP");
            return LOCALHOST_IPV4;
        }

        String ip = null;

        // 1. 检查X-Forwarded-For头（经过代理时）
        ip = getIpFromHeader(request, "X-Forwarded-For");
        if (isValidIp(ip)) {
            // X-Forwarded-For可能包含多个IP，取第一个
            return ip.split(",")[0].trim();
        }

        // 2. 检查X-Real-IP头（Nginx代理常用）
        ip = getIpFromHeader(request, "X-Real-IP");
        if (isValidIp(ip)) {
            return ip;
        }

        // 3. 检查Proxy-Client-IP头
        ip = getIpFromHeader(request, "Proxy-Client-IP");
        if (isValidIp(ip)) {
            return ip;
        }

        // 4. 检查WL-Proxy-Client-IP头（WebLogic代理）
        ip = getIpFromHeader(request, "WL-Proxy-Client-IP");
        if (isValidIp(ip)) {
            return ip;
        }

        // 5. 检查HTTP_CLIENT_IP头
        ip = getIpFromHeader(request, "HTTP_CLIENT_IP");
        if (isValidIp(ip)) {
            return ip;
        }

        // 6. 检查HTTP_X_FORWARDED_FOR头
        ip = getIpFromHeader(request, "HTTP_X_FORWARDED_FOR");
        if (isValidIp(ip)) {
            return ip.split(",")[0].trim();
        }

        // 7. 最后使用getRemoteAddr()方法获取
        ip = request.getRemoteAddr();
        if (isValidIp(ip)) {
            return ip;
        }

        log.warn("无法获取客户端IP，返回默认值");
        return LOCALHOST_IPV4;
    }

    /**
     * 从请求头中获取IP
     */
    private static String getIpFromHeader(HttpServletRequest request, String headerName) {
        String ip = request.getHeader(headerName);
        log.debug("从请求头 {} 获取IP: {}", headerName, ip);
        return ip;
    }

    /**
     * 验证IP地址是否有效
     */
    private static boolean isValidIp(String ip) {
        if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
            return false;
        }
        
        // 过滤内网IP和本地IP（可根据需要调整）
        if (LOCALHOST_IPV4.equals(ip) || LOCALHOST_IPV6.equals(ip)) {
            log.debug("检测到本地IP: {}", ip);
            return true;  // 开发环境允许本地IP
        }
        
        return true;
    }

    /**
     * 判断是否为内网IP
     */
    public static boolean isInternalIp(String ip) {
        if (ip == null || ip.isEmpty()) {
            return false;
        }

        try {
            // 127.x.x.x (本地回环)
            if (ip.startsWith("127.")) {
                return true;
            }
            
            // 10.x.x.x (A类私有地址)
            if (ip.startsWith("10.")) {
                return true;
            }
            
            // 172.16.x.x - 172.31.x.x (B类私有地址)
            if (ip.startsWith("172.")) {
                String[] parts = ip.split("\\.");
                if (parts.length >= 2) {
                    int secondOctet = Integer.parseInt(parts[1]);
                    if (secondOctet >= 16 && secondOctet <= 31) {
                        return true;
                    }
                }
            }
            
            // 192.168.x.x (C类私有地址)
            if (ip.startsWith("192.168.")) {
                return true;
            }
            
            // IPv6本地地址
            if (LOCALHOST_IPV6.equals(ip) || "::1".equals(ip)) {
                return true;
            }
            
        } catch (Exception e) {
            log.error("判断内网IP时发生错误: {}", e.getMessage());
        }
        
        return false;
    }

    /**
     * 获取用户代理信息
     */
    public static String getUserAgent(HttpServletRequest request) {
        if (request == null) {
            return "Unknown";
        }
        
        String userAgent = request.getHeader("User-Agent");
        return userAgent != null ? userAgent : "Unknown";
    }

    /**
     * 获取请求来源（Referer）
     */
    public static String getReferer(HttpServletRequest request) {
        if (request == null) {
            return "Unknown";
        }
        
        String referer = request.getHeader("Referer");
        return referer != null ? referer : "Direct";
    }
}