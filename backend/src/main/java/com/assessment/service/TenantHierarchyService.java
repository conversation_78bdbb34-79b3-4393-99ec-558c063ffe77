package com.assessment.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 租户层级管理服务
 * 处理多级组织架构的租户关系管理
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TenantHierarchyService {

    private final JdbcTemplate jdbcTemplate;

    /**
     * 组织类型枚举
     */
    public enum OrganizationType {
        GOVERNMENT("政府机构"),
        HOSPITAL("医院"),
        NURSING_HOME("养老院"),
        COMPANY("企业"),
        OTHER("其他");

        private final String displayName;

        OrganizationType(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    /**
     * 权限类型枚举
     */
    public enum PermissionType {
        READ, WRITE, ADMIN
    }

    /**
     * 权限范围枚举
     */
    public enum PermissionScope {
        ASSESSMENTS, USERS, REPORTS, ALL
    }

    /**
     * 租户层级DTO
     */
    public static class TenantHierarchyNode {
        private String tenantId;
        private String tenantCode;
        private String tenantName;
        private OrganizationType organizationType;
        private Integer level;
        private String parentTenantId;
        private List<TenantHierarchyNode> children = new ArrayList<>();
        
        // Getters and Setters
        public String getTenantId() { return tenantId; }
        public void setTenantId(String tenantId) { this.tenantId = tenantId; }
        
        public String getTenantCode() { return tenantCode; }
        public void setTenantCode(String tenantCode) { this.tenantCode = tenantCode; }
        
        public String getTenantName() { return tenantName; }
        public void setTenantName(String tenantName) { this.tenantName = tenantName; }
        
        public OrganizationType getOrganizationType() { return organizationType; }
        public void setOrganizationType(OrganizationType organizationType) { this.organizationType = organizationType; }
        
        public Integer getLevel() { return level; }
        public void setLevel(Integer level) { this.level = level; }
        
        public String getParentTenantId() { return parentTenantId; }
        public void setParentTenantId(String parentTenantId) { this.parentTenantId = parentTenantId; }
        
        public List<TenantHierarchyNode> getChildren() { return children; }
        public void setChildren(List<TenantHierarchyNode> children) { this.children = children; }
    }

    /**
     * 获取租户的完整层级树
     */
    @Transactional(readOnly = true)
    public List<TenantHierarchyNode> getTenantHierarchyTree(String rootTenantId) {
        log.info("获取租户层级树: rootTenantId={}", rootTenantId);
        
        // 获取所有相关租户
        String sql = """
            WITH RECURSIVE tenant_tree AS (
                -- 起始节点
                SELECT id, code, name, organization_type, tenant_level, parent_tenant_id, 0 as depth
                FROM tenants 
                WHERE id = CAST(? AS UUID)
                
                UNION ALL
                
                -- 递归获取子节点
                SELECT t.id, t.code, t.name, t.organization_type, t.tenant_level, t.parent_tenant_id, tt.depth + 1
                FROM tenants t
                INNER JOIN tenant_tree tt ON t.parent_tenant_id = tt.id
                WHERE tt.depth < 10  -- 防止无限递归
            )
            SELECT id, code, name, organization_type, tenant_level, parent_tenant_id, depth
            FROM tenant_tree
            ORDER BY depth, name
            """;
        
        List<Map<String, Object>> results = jdbcTemplate.queryForList(sql, rootTenantId);
        
        // 构建层级树
        Map<String, TenantHierarchyNode> nodeMap = new HashMap<>();
        List<TenantHierarchyNode> rootNodes = new ArrayList<>();
        
        for (Map<String, Object> row : results) {
            TenantHierarchyNode node = new TenantHierarchyNode();
            node.setTenantId(row.get("id").toString());
            node.setTenantCode((String) row.get("code"));
            node.setTenantName((String) row.get("name"));
            node.setOrganizationType(OrganizationType.valueOf((String) row.get("organization_type")));
            node.setLevel((Integer) row.get("tenant_level"));
            node.setParentTenantId(row.get("parent_tenant_id") != null ? row.get("parent_tenant_id").toString() : null);
            
            nodeMap.put(node.getTenantId(), node);
            
            if (node.getParentTenantId() == null || node.getParentTenantId().equals(rootTenantId)) {
                rootNodes.add(node);
            }
        }
        
        // 构建父子关系
        for (TenantHierarchyNode node : nodeMap.values()) {
            if (node.getParentTenantId() != null && nodeMap.containsKey(node.getParentTenantId())) {
                TenantHierarchyNode parent = nodeMap.get(node.getParentTenantId());
                parent.getChildren().add(node);
            }
        }
        
        return rootNodes;
    }

    /**
     * 获取租户的所有下级租户ID
     */
    @Transactional(readOnly = true)
    public List<String> getDescendantTenantIds(String tenantId, Integer maxDepth) {
        log.info("获取下级租户ID: tenantId={}, maxDepth={}", tenantId, maxDepth);
        
        String sql = """
            SELECT descendant_tenant_id::text
            FROM tenant_hierarchies 
            WHERE ancestor_tenant_id = CAST(? AS UUID)
              AND depth > 0
              AND (? IS NULL OR depth <= ?)
            ORDER BY depth
            """;
        
        return jdbcTemplate.queryForList(sql, String.class, tenantId, maxDepth, maxDepth);
    }

    /**
     * 获取租户的所有上级租户ID
     */
    @Transactional(readOnly = true)
    public List<String> getAncestorTenantIds(String tenantId) {
        log.info("获取上级租户ID: tenantId={}", tenantId);
        
        String sql = """
            SELECT ancestor_tenant_id::text
            FROM tenant_hierarchies 
            WHERE descendant_tenant_id = CAST(? AS UUID)
              AND depth > 0
            ORDER BY depth DESC
            """;
        
        return jdbcTemplate.queryForList(sql, String.class, tenantId);
    }

    /**
     * 检查租户是否有权限访问目标租户的数据
     */
    @Transactional(readOnly = true)
    public boolean hasDataPermission(String sourceTenantId, String targetTenantId, 
                                   PermissionType permissionType, PermissionScope permissionScope) {
        log.debug("检查数据权限: source={}, target={}, type={}, scope={}", 
                sourceTenantId, targetTenantId, permissionType, permissionScope);
        
        String sql = "SELECT has_tenant_data_permission(CAST(? AS UUID), CAST(? AS UUID), ?, ?)";
        
        Boolean result = jdbcTemplate.queryForObject(sql, Boolean.class,
                sourceTenantId, targetTenantId, permissionType.name(), permissionScope.name());
        
        return result != null && result;
    }

    /**
     * 获取租户可以访问的所有目标租户ID
     */
    @Transactional(readOnly = true)
    public List<String> getAccessibleTenantIds(String sourceTenantId, PermissionType permissionType, PermissionScope permissionScope) {
        log.info("获取可访问租户ID: source={}, type={}, scope={}", sourceTenantId, permissionType, permissionScope);
        
        // 首先获取自己
        List<String> accessibleIds = new ArrayList<>();
        accessibleIds.add(sourceTenantId);
        
        // 获取有权限访问的下级租户
        String sql = """
            SELECT DISTINCT p.target_tenant_id::text
            FROM tenant_data_permissions p
            WHERE p.source_tenant_id = CAST(? AS UUID)
              AND p.permission_type = ?
              AND (p.permission_scope = ? OR p.permission_scope = 'ALL')
              AND p.is_active = true
              AND (p.expires_at IS NULL OR p.expires_at > CURRENT_TIMESTAMP)
            """;
        
        List<String> permittedIds = jdbcTemplate.queryForList(sql, String.class,
                sourceTenantId, permissionType.name(), permissionScope.name());
        
        accessibleIds.addAll(permittedIds);
        
        return accessibleIds.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 授予租户数据访问权限
     */
    @Transactional
    public void grantDataPermission(String sourceTenantId, String targetTenantId,
                                  PermissionType permissionType, PermissionScope permissionScope,
                                  String grantedBy) {
        log.info("授予数据权限: source={}, target={}, type={}, scope={}, grantedBy={}", 
                sourceTenantId, targetTenantId, permissionType, permissionScope, grantedBy);
        
        String sql = """
            INSERT INTO tenant_data_permissions 
            (source_tenant_id, target_tenant_id, permission_type, permission_scope, granted_by)
            VALUES (CAST(? AS UUID), CAST(? AS UUID), ?, ?, CAST(? AS UUID))
            ON CONFLICT (source_tenant_id, target_tenant_id, permission_type, permission_scope)
            DO UPDATE SET 
                is_active = true,
                granted_by = CAST(? AS UUID),
                granted_at = CURRENT_TIMESTAMP
            """;
        
        jdbcTemplate.update(sql, sourceTenantId, targetTenantId, permissionType.name(), 
                permissionScope.name(), grantedBy, grantedBy);
    }

    /**
     * 撤销租户数据访问权限
     */
    @Transactional
    public void revokeDataPermission(String sourceTenantId, String targetTenantId,
                                   PermissionType permissionType, PermissionScope permissionScope) {
        log.info("撤销数据权限: source={}, target={}, type={}, scope={}", 
                sourceTenantId, targetTenantId, permissionType, permissionScope);
        
        String sql = """
            UPDATE tenant_data_permissions 
            SET is_active = false
            WHERE source_tenant_id = CAST(? AS UUID)
              AND target_tenant_id = CAST(? AS UUID)
              AND permission_type = ?
              AND permission_scope = ?
            """;
        
        jdbcTemplate.update(sql, sourceTenantId, targetTenantId, permissionType.name(), permissionScope.name());
    }

    /**
     * 更新租户层级关系
     */
    @Transactional
    public void updateTenantHierarchy(String tenantId, String newParentId) {
        log.info("更新租户层级关系: tenantId={}, newParentId={}", tenantId, newParentId);
        
        // 删除旧的层级关系
        String deleteSql = """
            DELETE FROM tenant_hierarchies 
            WHERE descendant_tenant_id = CAST(? AS UUID) AND depth > 0
            """;
        jdbcTemplate.update(deleteSql, tenantId);
        
        // 更新父租户
        String updateParentSql = """
            UPDATE tenants 
            SET parent_tenant_id = CAST(? AS UUID),
                updated_at = CURRENT_TIMESTAMP
            WHERE id = CAST(? AS UUID)
            """;
        jdbcTemplate.update(updateParentSql, newParentId, tenantId);
        
        // 重建层级关系
        rebuildHierarchyForTenant(tenantId);
    }

    /**
     * 重建指定租户的层级关系
     */
    @Transactional
    public void rebuildHierarchyForTenant(String tenantId) {
        log.info("重建租户层级关系: tenantId={}", tenantId);
        
        // 获取租户的父级路径
        String pathSql = """
            WITH RECURSIVE tenant_path AS (
                SELECT id, parent_tenant_id, 1 as depth, ARRAY[id] as path
                FROM tenants 
                WHERE id = CAST(? AS UUID)
                
                UNION ALL
                
                SELECT t.id, t.parent_tenant_id, tp.depth + 1, tp.path || t.id
                FROM tenants t
                INNER JOIN tenant_path tp ON t.id = tp.parent_tenant_id
                WHERE tp.depth < 10
            )
            SELECT path FROM tenant_path ORDER BY depth DESC LIMIT 1
            """;
        
        try {
            Object[] path = (Object[]) jdbcTemplate.queryForObject(pathSql, Object[].class, tenantId);
            
            if (path != null) {
                // 插入新的层级关系
                for (int i = 0; i < path.length - 1; i++) {
                    String ancestorId = path[i].toString();
                    int depth = path.length - 1 - i;
                    
                    String insertSql = """
                        INSERT INTO tenant_hierarchies (ancestor_tenant_id, descendant_tenant_id, depth)
                        VALUES (CAST(? AS UUID), CAST(? AS UUID), ?)
                        ON CONFLICT (ancestor_tenant_id, descendant_tenant_id) DO NOTHING
                        """;
                    
                    jdbcTemplate.update(insertSql, ancestorId, tenantId, depth);
                }
            }
        } catch (Exception e) {
            log.warn("重建租户层级关系时出现异常: {}", e.getMessage());
        }
    }

    /**
     * 获取租户的面包屑导航路径
     */
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getTenantBreadcrumb(String tenantId) {
        log.debug("获取租户面包屑: tenantId={}", tenantId);
        
        String sql = """
            WITH RECURSIVE tenant_path AS (
                SELECT id, code, name, parent_tenant_id, organization_type, tenant_level, 0 as depth
                FROM tenants 
                WHERE id = CAST(? AS UUID)
                
                UNION ALL
                
                SELECT t.id, t.code, t.name, t.parent_tenant_id, t.organization_type, t.tenant_level, tp.depth + 1
                FROM tenants t
                INNER JOIN tenant_path tp ON t.id = tp.parent_tenant_id
                WHERE tp.depth < 10
            )
            SELECT id::text as tenantId, code, name, organization_type as organizationType, tenant_level as level
            FROM tenant_path 
            ORDER BY depth DESC
            """;
        
        return jdbcTemplate.queryForList(sql, tenantId);
    }

    /**
     * 检查租户层级关系的有效性
     */
    @Transactional(readOnly = true)
    public boolean isValidHierarchy(String parentId, String childId) {
        // 检查是否会形成循环引用
        String sql = """
            SELECT COUNT(*) > 0
            FROM tenant_hierarchies 
            WHERE ancestor_tenant_id = CAST(? AS UUID) 
              AND descendant_tenant_id = CAST(? AS UUID)
            """;
        
        Boolean wouldCreateCycle = jdbcTemplate.queryForObject(sql, Boolean.class, childId, parentId);
        return wouldCreateCycle == null || !wouldCreateCycle;
    }
}