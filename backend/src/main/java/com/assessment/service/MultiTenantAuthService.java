package com.assessment.service;

import com.assessment.dto.MultiTenantLoginRequest;
import com.assessment.dto.MultiTenantLoginResponse;
import com.assessment.entity.multitenant.PlatformUser;
import com.assessment.entity.multitenant.Tenant;
import com.assessment.entity.multitenant.TenantUserMembership;
import com.assessment.repository.multitenant.PlatformUserRepository;
import com.assessment.repository.multitenant.TenantRepository;
import com.assessment.repository.multitenant.TenantUserMembershipRepository;
import java.util.Arrays;
import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/** 多租户认证服务 处理租户名+用户名+密码的登录逻辑 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MultiTenantAuthService {

  private final PlatformUserRepository userRepository;
  private final TenantRepository tenantRepository;
  private final TenantUserMembershipRepository membershipRepository;
  private final PasswordEncoder passwordEncoder;
  private final JwtTokenService jwtTokenService;
  private final TenantHierarchyService tenantHierarchyService;
  private final UserIdentityService userIdentityService;

  /** 多租户登录认证 */
  @Transactional
  public MultiTenantLoginResponse authenticate(MultiTenantLoginRequest request) {
    // 使用安全日志记录（脱敏处理）
    String secureLogInfo = userIdentityService.createSecureLogInfo(request.getUsername(), request.getTenantCode());
    log.info("多租户登录尝试: {}", secureLogInfo);

    try {
      // 0. 验证用户名和机构代码格式
      validateLoginCredentials(request);

      // 1. 处理超级管理员登录
      if (isSuperAdminLogin(request)) {
        return authenticateSuperAdmin(request);
      }

      // 2. 验证租户是否存在且活跃
      Tenant tenant = validateTenant(request.getTenantCode());

      // 3. 验证用户认证
      PlatformUser user = authenticateUser(request);

      // 4. 验证用户租户关系
      TenantUserMembership membership =
          validateTenantMembership(user, tenant);

      // 5. 更新最后登录时间
      updateLastLoginTime(user, membership);

      // 6. 生成JWT Token
      String accessToken = jwtTokenService.generateAccessToken(user, tenant, membership);
      String refreshToken = jwtTokenService.generateRefreshToken(user.getId().toString());

      // 7. 构建响应
      return buildLoginResponse(user, tenant, membership, accessToken, refreshToken);

    } catch (Exception e) {
      log.error("多租户登录失败: {}, 错误={}", secureLogInfo, e.getMessage());
      throw new BadCredentialsException("登录失败: " + e.getMessage());
    }
  }

  /** 验证登录凭据格式 */
  private void validateLoginCredentials(MultiTenantLoginRequest request) {
    if (request.getUsername() == null || request.getUsername().trim().isEmpty()) {
      throw new BadCredentialsException("用户名不能为空");
    }
    
    if (request.getTenantCode() == null || request.getTenantCode().trim().isEmpty()) {
      throw new BadCredentialsException("机构代码不能为空");
    }
    
    if (request.getPassword() == null || request.getPassword().trim().isEmpty()) {
      throw new BadCredentialsException("密码不能为空");
    }
    
    // 验证用户名格式（除超级管理员外）
    if (!isSuperAdminLogin(request) && !userIdentityService.isValidUsername(request.getUsername())) {
      throw new BadCredentialsException("用户名格式错误，应为：姓名拼音.工号（如：zhangsan.001）");
    }
    
    // 验证机构代码格式
    if (!userIdentityService.isValidTenantCode(request.getTenantCode())) {
      throw new BadCredentialsException("机构代码格式错误，请检查输入");
    }
    
    // 验证用户名和机构代码组合的合理性
    if (!isSuperAdminLogin(request) && 
        !userIdentityService.isValidCombination(request.getUsername(), request.getTenantCode())) {
      throw new BadCredentialsException("用户名与机构代码组合不合理");
    }
    
    log.debug("登录凭据格式验证通过");
  }

  /** 检查是否为超级管理员登录 */
  private boolean isSuperAdminLogin(MultiTenantLoginRequest request) {
    return "superadmin".equals(request.getUsername())
        || ("platform".equals(request.getTenantCode()) && "admin".equals(request.getUsername()))
        || ("SYSTEM".equals(request.getTenantCode()) && "admin".equals(request.getUsername()));
  }

  /** 超级管理员认证 */
  private MultiTenantLoginResponse authenticateSuperAdmin(MultiTenantLoginRequest request) {
    PlatformUser superAdmin =
        userRepository
            .findByUsername(request.getUsername())
            .orElseThrow(() -> new BadCredentialsException("超级管理员账户不存在"));

    if (!passwordEncoder.matches(request.getPassword(), superAdmin.getPasswordHash())) {
      throw new BadCredentialsException("超级管理员密码错误");
    }

    if (!PlatformUser.PlatformRole.ADMIN.equals(superAdmin.getPlatformRole())) {
      throw new BadCredentialsException("账户无超级管理员权限");
    }

    // 暂时注释掉最后登录时间更新，避免UUID映射问题
    // superAdmin.setLastLoginAt(LocalDateTime.now());
    // userRepository.save(superAdmin);

    // 生成超级管理员Token
    String accessToken = jwtTokenService.generateSuperAdminToken(superAdmin);
    String refreshToken = jwtTokenService.generateRefreshToken(superAdmin.getId().toString());

    return MultiTenantLoginResponse.builder()
        .accessToken(accessToken)
        .refreshToken(refreshToken)
        .tokenType("Bearer")
        .expiresIn(3600) // 1小时
        .userId(superAdmin.getId().toString())
        .username(superAdmin.getUsername())
        .email(superAdmin.getEmail())
        .firstName(superAdmin.getFirstName())
        .lastName(superAdmin.getLastName())
        .platformRole(superAdmin.getPlatformRole().name())
        .isSuperAdmin(true)
        .isActive(superAdmin.getIsActive())
        .lastLoginAt(superAdmin.getLastLoginAt())
        .permissions(Arrays.asList("ALL")) // 超级管理员拥有所有权限
        .build();
  }

  /** 验证租户 */
  private Tenant validateTenant(String tenantCode) {
    Tenant tenant =
        tenantRepository
            .findByCode(tenantCode)
            .orElseThrow(() -> new BadCredentialsException("租户不存在: " + tenantCode));

    if (!Tenant.TenantStatus.ACTIVE.equals(tenant.getStatus())) {
      throw new BadCredentialsException("租户已禁用: " + tenantCode);
    }

    if (Tenant.SubscriptionStatus.SUSPENDED.equals(tenant.getSubscriptionStatus())) {
      throw new BadCredentialsException("租户订阅已暂停: " + tenantCode);
    }

    return tenant;
  }

  /** 验证用户认证 */
  private PlatformUser authenticateUser(MultiTenantLoginRequest request) {
    String fullUsername = request.getFullUsername();

    PlatformUser user =
        userRepository
            .findByUsername(fullUsername)
            .orElseThrow(() -> new BadCredentialsException("用户不存在: " + request.getUsername()));

    if (!passwordEncoder.matches(request.getPassword(), user.getPasswordHash())) {
      throw new BadCredentialsException("密码错误");
    }

    if (!user.getIsActive()) {
      throw new BadCredentialsException("用户账户已禁用");
    }

    return user;
  }

  /** 验证用户租户关系 */
  private TenantUserMembership validateTenantMembership(PlatformUser user, Tenant tenant) {
    try {
      log.debug("验证用户租户关系: userId={}, tenantId={}", user.getId(), tenant.getId());
      
      TenantUserMembership membership =
          membershipRepository
              .findByTenantIdAndUserId(tenant.getId().toString(), user.getId().toString())
              .orElseThrow(() -> new BadCredentialsException("用户不属于该租户"));

      if (!TenantUserMembership.MembershipStatus.ACTIVE.equals(membership.getStatus())) {
        throw new BadCredentialsException("用户在该租户中的状态异常");
      }

      return membership;
    } catch (Exception e) {
      log.error("验证用户租户关系失败: {}", e.getMessage(), e);
      throw e;
    }
  }

  /** 更新最后登录时间 */
  private void updateLastLoginTime(PlatformUser user, TenantUserMembership membership) {
    // 暂时跳过最后登录时间更新，避免UUID映射问题
    // 后续可以使用原生SQL或修复实体映射来解决
    log.info("用户 {} 在租户 {} 中登录成功，跳过活跃时间更新", user.getUsername(), membership.getTenantId());
  }

  /** 构建登录响应 */
  private MultiTenantLoginResponse buildLoginResponse(
      PlatformUser user,
      Tenant tenant,
      TenantUserMembership membership,
      String accessToken,
      String refreshToken) {

    // 获取用户权限
    List<String> permissions = new ArrayList<>(Arrays.asList(membership.getTenantRole().getDefaultPermissions()));
    
    // 获取可访问的租户列表（包含层级权限）
    List<String> accessibleTenantIds = getAccessibleTenants(tenant.getId().toString(), membership.getTenantRole());

    return MultiTenantLoginResponse.builder()
        .accessToken(accessToken)
        .refreshToken(refreshToken)
        .tokenType("Bearer")
        .expiresIn(3600) // 1小时

        // 用户信息
        .userId(user.getId().toString())
        .username(user.getUsername())
        .email(user.getEmail())
        .firstName(user.getFirstName())
        .lastName(user.getLastName())
        .platformRole(user.getPlatformRole().name())

        // 租户信息
        .tenantId(tenant.getId().toString())
        .tenantCode(tenant.getCode())
        .tenantName(tenant.getName())
        .tenantIndustry(tenant.getIndustry())
        .subscriptionPlan(tenant.getSubscriptionPlan().name())

        // 租户角色信息
        .tenantRole(membership.getTenantRole().name())
        .displayName(membership.getDisplayName())
        .department(membership.getDepartment())
        .professionalTitle(membership.getProfessionalTitle())
        .permissions(permissions)
        .accessibleTenantIds(accessibleTenantIds)

        // 状态信息
        .isActive(user.getIsActive())
        .isSuperAdmin(false)
        .lastLoginAt(user.getLastLoginAt())
        .joinedAt(membership.getJoinedAt())

        // 租户配置
        .tenantConfig(
            MultiTenantLoginResponse.TenantConfig.builder()
                .maxUsers(tenant.getMaxUsers())
                .maxMonthlyAssessments(tenant.getMaxMonthlyAssessments())
                .maxCustomScales(tenant.getMaxCustomScales())
                .enableCustomScales(true)
                .enableReports(true)
                .enableAuditLogs(true)
                .build())
        .build();
  }

  /**
   * 获取用户可访问的租户列表（包含层级权限）
   * 根据用户角色和组织层级关系确定可访问的租户范围
   */
  private List<String> getAccessibleTenants(String tenantId, TenantUserMembership.TenantRole tenantRole) {
    log.debug("获取可访问租户列表: tenantId={}, role={}", tenantId, tenantRole);
    
    List<String> accessibleTenants = new ArrayList<>();
    
    try {
      // 根据角色确定层级访问权限
      switch (tenantRole) {
        case ADMIN:
          // 管理员可以访问所有下级租户的数据（包含当前租户）
          List<String> descendants = tenantHierarchyService.getAccessibleTenantIds(
              tenantId, 
              TenantHierarchyService.PermissionType.READ, 
              TenantHierarchyService.PermissionScope.ALL
          );
          accessibleTenants.addAll(descendants);
          log.info("管理员 {} 可访问 {} 个租户", tenantId, accessibleTenants.size());
          break;
          
        case SUPERVISOR:
          // 督导员可以访问直接下级租户的评估数据
          List<String> directDescendants = tenantHierarchyService.getDescendantTenantIds(tenantId, 1);
          for (String descendantId : directDescendants) {
            if (tenantHierarchyService.hasDataPermission(
                tenantId, descendantId, 
                TenantHierarchyService.PermissionType.READ, 
                TenantHierarchyService.PermissionScope.ASSESSMENTS)) {
              accessibleTenants.add(descendantId);
            }
          }
          log.info("督导员 {} 可访问 {} 个租户", tenantId, accessibleTenants.size());
          break;
          
        default:
          // 其他角色仅能访问当前租户
          accessibleTenants.add(tenantId);
          log.info("普通用户 {} 仅可访问当前租户", tenantId);
          break;
      }
      
    } catch (Exception e) {
      log.warn("获取可访问租户列表时出现异常: {}", e.getMessage());
      // 发生异常时仅返回当前租户
      accessibleTenants.clear();
      accessibleTenants.add(tenantId);
    }
    
    return accessibleTenants.stream().distinct().collect(Collectors.toList());
  }
}
