package com.assessment.service;

import com.assessment.common.response.ApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.security.SecureRandom;
import java.time.Duration;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 简单滑动验证码服务
 * 基于Java AWT实现的滑动拼图验证码
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-23
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SimpleCaptchaService {

    private final RedisTemplate<String, Object> redisTemplate;
    
    private static final String CAPTCHA_PREFIX = "simple_captcha:";
    private static final int CAPTCHA_WIDTH = 310;
    private static final int CAPTCHA_HEIGHT = 155;
    private static final int PIECE_WIDTH = 47;
    private static final int PIECE_HEIGHT = 47;
    private static final int TOLERANCE = 5; // 允许的误差像素
    
    private final SecureRandom random = new SecureRandom();
    
    /**
     * 生成滑动验证码
     */
    public ApiResponse<Map<String, Object>> generateCaptcha() {
        try {
            String token = UUID.randomUUID().toString();
            
            // 生成随机滑块位置
            int x = random.nextInt(CAPTCHA_WIDTH - PIECE_WIDTH - 50) + 50; // 留出起始和结束位置
            int y = random.nextInt(CAPTCHA_HEIGHT - PIECE_HEIGHT - 20) + 10; // 留出上下边距
            
            // 创建背景图
            BufferedImage backgroundImage = createBackgroundImage();
            
            // 创建滑块图片和背景缺口
            BufferedImage pieceImage = createPieceImage(backgroundImage, x, y);
            
            // 将图片转为Base64
            String backgroundBase64 = imageToBase64(backgroundImage);
            String pieceBase64 = imageToBase64(pieceImage);
            
            // 生成验证密钥
            String secretKey = generateSecretKey();
            
            // 存储验证信息到Redis
            Map<String, Object> captchaInfo = new HashMap<>();
            captchaInfo.put("x", x);
            captchaInfo.put("y", y);
            captchaInfo.put("secretKey", secretKey);
            captchaInfo.put("timestamp", System.currentTimeMillis());
            
            redisTemplate.opsForValue().set(
                CAPTCHA_PREFIX + token, 
                captchaInfo, 
                Duration.ofMinutes(5)
            );
            
            // 返回验证码数据
            Map<String, Object> result = new HashMap<>();
            result.put("token", token);
            result.put("originalImageBase64", backgroundBase64);
            result.put("jigsawImageBase64", pieceBase64);
            result.put("secretKey", secretKey);
            result.put("result", false);
            result.put("y", y); // 添加Y坐标到响应中
            
            log.info("验证码生成成功，token: {}, 滑块位置: ({}, {})", token, x, y);
            return ApiResponse.success(result);
            
        } catch (Exception e) {
            log.error("生成验证码失败", e);
            return ApiResponse.error("验证码生成失败");
        }
    }
    
    /**
     * 校验滑动验证码
     */
    public ApiResponse<Map<String, Object>> checkCaptcha(String token, String pointJson, String verification) {
        try {
            if (token == null || pointJson == null || verification == null) {
                return ApiResponse.error("参数不完整");
            }
            
            // 从Redis获取验证信息
            Object captchaObj = redisTemplate.opsForValue().get(CAPTCHA_PREFIX + token);
            if (captchaObj == null) {
                return ApiResponse.error("验证码已过期或无效");
            }
            
            @SuppressWarnings("unchecked")
            Map<String, Object> captchaInfo = (Map<String, Object>) captchaObj;
            
            // 验证密钥
            String storedSecretKey = (String) captchaInfo.get("secretKey");
            if (!verification.equals(storedSecretKey)) {
                return ApiResponse.error("验证密钥错误");
            }
            
            // 解析滑动位置
            String cleanPointJson = pointJson.replace("{", "").replace("}", "").replace("\"", "");
            String[] pairs = cleanPointJson.split(",");
            int userX = 0;
            
            for (String pair : pairs) {
                String[] kv = pair.split(":");
                if (kv.length == 2 && "x".equals(kv[0].trim())) {
                    userX = Integer.parseInt(kv[1].trim());
                    break;
                }
            }
            
            // 获取正确位置
            int correctX = (Integer) captchaInfo.get("x");
            
            // 验证位置是否正确（允许一定误差）
            boolean isValid = Math.abs(userX - correctX) <= TOLERANCE;
            
            Map<String, Object> result = new HashMap<>();
            result.put("result", isValid);
            result.put("message", isValid ? "验证成功" : "验证失败");
            
            if (isValid) {
                // 验证成功，删除Redis中的数据
                redisTemplate.delete(CAPTCHA_PREFIX + token);
                log.info("验证码校验成功，token: {}, 用户位置: {}, 正确位置: {}", token, userX, correctX);
            } else {
                log.warn("验证码校验失败，token: {}, 用户位置: {}, 正确位置: {}", token, userX, correctX);
            }
            
            return ApiResponse.success(result);
            
        } catch (Exception e) {
            log.error("校验验证码失败", e);
            return ApiResponse.error("验证码校验失败");
        }
    }
    
    /**
     * 创建背景图片
     */
    private BufferedImage createBackgroundImage() {
        BufferedImage image = new BufferedImage(CAPTCHA_WIDTH, CAPTCHA_HEIGHT, BufferedImage.TYPE_INT_RGB);
        Graphics2D g = image.createGraphics();
        
        // 设置高质量渲染
        g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        g.setRenderingHint(RenderingHints.KEY_COLOR_RENDERING, RenderingHints.VALUE_COLOR_RENDER_QUALITY);
        
        // 创建更柔和的渐变背景
        GradientPaint gradient = new GradientPaint(
            0, 0, new Color(240, 249, 255), // 淡蓝色
            CAPTCHA_WIDTH, CAPTCHA_HEIGHT, new Color(224, 237, 255) // 稍深的淡蓝色
        );
        g.setPaint(gradient);
        g.fillRect(0, 0, CAPTCHA_WIDTH, CAPTCHA_HEIGHT);
        
        // 添加网格图案
        g.setColor(new Color(200, 220, 240, 30));
        g.setStroke(new BasicStroke(1));
        for (int i = 0; i < CAPTCHA_WIDTH; i += 20) {
            g.drawLine(i, 0, i, CAPTCHA_HEIGHT);
        }
        for (int i = 0; i < CAPTCHA_HEIGHT; i += 20) {
            g.drawLine(0, i, CAPTCHA_WIDTH, i);
        }
        
        // 添加装饰性圆形（更精致）
        for (int i = 0; i < 6; i++) {
            int x = random.nextInt(CAPTCHA_WIDTH - 40) + 20;
            int y = random.nextInt(CAPTCHA_HEIGHT - 40) + 20;
            int size = random.nextInt(30) + 20;
            
            // 外圈
            g.setColor(new Color(100, 150, 200, 40));
            g.fillOval(x - 2, y - 2, size + 4, size + 4);
            
            // 内圈
            g.setColor(new Color(255, 255, 255, 60));
            g.fillOval(x, y, size, size);
        }
        
        // 添加随机风景元素（简单的山形）
        g.setColor(new Color(150, 200, 230, 50));
        int[] xPoints = {0, 80, 160, 240, CAPTCHA_WIDTH};
        int[] yPoints = {CAPTCHA_HEIGHT, 
                        CAPTCHA_HEIGHT - random.nextInt(40) - 20,
                        CAPTCHA_HEIGHT - random.nextInt(60) - 30,
                        CAPTCHA_HEIGHT - random.nextInt(40) - 20,
                        CAPTCHA_HEIGHT};
        g.fillPolygon(xPoints, yPoints, 5);
        
        g.dispose();
        return image;
    }
    
    /**
     * 创建滑块图片并在背景图上挖洞
     */
    private BufferedImage createPieceImage(BufferedImage backgroundImage, int x, int y) {
        // 创建滑块图片，高度与背景图相同
        BufferedImage pieceImage = new BufferedImage(PIECE_WIDTH, CAPTCHA_HEIGHT, BufferedImage.TYPE_INT_ARGB);
        Graphics2D pieceG = pieceImage.createGraphics();
        pieceG.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        
        // 设置透明背景
        pieceG.setComposite(AlphaComposite.Clear);
        pieceG.fillRect(0, 0, PIECE_WIDTH, CAPTCHA_HEIGHT);
        
        // 恢复正常绘制模式
        pieceG.setComposite(AlphaComposite.SrcOver);
        
        // 创建拼图形状路径
        java.awt.Shape puzzleShape = createPuzzleShapePath(0, y, PIECE_WIDTH, PIECE_HEIGHT);
        
        // 只创建拼图形状的透明轮廓，前端负责渲染颜色
        pieceG.setColor(new Color(255, 255, 255, 255)); // 白色填充，前端可以通过CSS改变颜色
        pieceG.fill(puzzleShape);
        
        // 添加黑色边框，便于前端识别拼图形状
        pieceG.setStroke(new BasicStroke(2.0f));
        pieceG.setColor(new Color(0, 0, 0, 255)); // 黑色边框
        pieceG.draw(puzzleShape);
        
        pieceG.dispose();
        
        // 在背景图上挖洞
        Graphics2D bgG = backgroundImage.createGraphics();
        bgG.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        
        java.awt.Shape bgPuzzleShape = createPuzzleShapePath(x, y, PIECE_WIDTH, PIECE_HEIGHT);
        
        // 先在缺口区域填充深色，模拟凹陷效果
        bgG.setColor(new Color(0, 0, 0, 40)); // 半透明黑色
        bgG.fill(bgPuzzleShape);
        
        // 添加内部阴影效果 - 模拟凹陷
        bgG.setStroke(new BasicStroke(2.0f));
        bgG.setColor(new Color(0, 0, 0, 80)); // 更深的阴影
        bgG.draw(bgPuzzleShape);
        
        // 清除拼图形状区域，但保留边缘效果
        Graphics2D tempBgG = (Graphics2D) bgG.create();
        tempBgG.setComposite(AlphaComposite.Clear);
        // 创建稍小的形状来清除，保留边缘阴影
        java.awt.Shape innerShape = createPuzzleShapePath(x + 1, y + 1, PIECE_WIDTH - 2, PIECE_HEIGHT - 2);
        tempBgG.fill(innerShape);
        tempBgG.dispose();
        
        // 在缺口边缘添加凹陷高光效果
        bgG.setComposite(AlphaComposite.SrcOver);
        bgG.setColor(new Color(255, 255, 255, 60)); // 淡白色高光
        bgG.setStroke(new BasicStroke(1.0f));
        // 只在顶部和左侧添加高光
        Graphics2D highlightG = (Graphics2D) bgG.create();
        highlightG.setClip(bgPuzzleShape);
        highlightG.drawLine(x, y, x + PIECE_WIDTH, y); // 顶部高光
        highlightG.drawLine(x, y, x, y + PIECE_HEIGHT); // 左侧高光
        highlightG.dispose();
        
        bgG.dispose();
        
        return pieceImage;
    }
    
    /**
     * 创建拼图形状路径
     */
    private java.awt.Shape createPuzzleShapePath(int x, int y, int width, int height) {
        java.awt.geom.Path2D path = new java.awt.geom.Path2D.Double();
        
        int tabSize = 12; // 凸起大小
        int cornerRadius = 3; // 圆角半径
        
        // 开始路径（左上角）
        path.moveTo(x + cornerRadius, y);
        
        // 顶部边线（带圆角）
        path.lineTo(x + width - cornerRadius, y);
        path.quadTo(x + width, y, x + width, y + cornerRadius);
        
        // 右侧边线（带凸起）
        path.lineTo(x + width, y + height/2 - tabSize);
        // 右侧凸起（圆形）
        path.curveTo(x + width, y + height/2 - tabSize/2,
                     x + width + tabSize, y + height/2 - tabSize/2,
                     x + width + tabSize, y + height/2);
        path.curveTo(x + width + tabSize, y + height/2 + tabSize/2,
                     x + width, y + height/2 + tabSize/2,
                     x + width, y + height/2 + tabSize);
        
        // 继续右侧边线
        path.lineTo(x + width, y + height - cornerRadius);
        path.quadTo(x + width, y + height, x + width - cornerRadius, y + height);
        
        // 底部边线（带凸起）
        path.lineTo(x + width/2 + tabSize, y + height);
        // 底部凸起（圆形）
        path.curveTo(x + width/2 + tabSize/2, y + height,
                     x + width/2 + tabSize/2, y + height + tabSize,
                     x + width/2, y + height + tabSize);
        path.curveTo(x + width/2 - tabSize/2, y + height + tabSize,
                     x + width/2 - tabSize/2, y + height,
                     x + width/2 - tabSize, y + height);
        
        // 继续底部边线
        path.lineTo(x + cornerRadius, y + height);
        path.quadTo(x, y + height, x, y + height - cornerRadius);
        
        // 左侧边线
        path.lineTo(x, y + cornerRadius);
        path.quadTo(x, y, x + cornerRadius, y);
        
        path.closePath();
        
        return path;
    }
    
    
    /**
     * 图片转Base64
     */
    private String imageToBase64(BufferedImage image) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(image, "PNG", baos);
        byte[] imageBytes = baos.toByteArray();
        return Base64.getEncoder().encodeToString(imageBytes);
    }
    
    /**
     * 生成验证密钥
     */
    private String generateSecretKey() {
        return UUID.randomUUID().toString().replace("-", "");
    }
}