package com.assessment.repository;

import com.assessment.entity.IndividualUser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 个人用户数据访问层
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-23
 */
@Repository
public interface IndividualUserRepository extends JpaRepository<IndividualUser, UUID> {

    /**
     * 根据邮箱查找用户
     */
    Optional<IndividualUser> findByEmail(String email);

    /**
     * 根据手机号查找用户
     */
    Optional<IndividualUser> findByPhone(String phone);

    /**
     * 根据邮箱或手机号查找用户（用于登录）
     */
    @Query("SELECT u FROM IndividualUser u WHERE u.email = :identifier OR u.phone = :identifier")
    Optional<IndividualUser> findByEmailOrPhone(@Param("identifier") String identifier);

    /**
     * 检查邮箱是否已存在
     */
    boolean existsByEmail(String email);

    /**
     * 检查手机号是否已存在
     */
    boolean existsByPhone(String phone);

    /**
     * 根据状态查找用户
     */
    List<IndividualUser> findByStatus(IndividualUser.AccountStatus status);

    /**
     * 根据服务类型查找用户
     */
    List<IndividualUser> findByServiceType(IndividualUser.ServiceType serviceType);

    /**
     * 查找活跃的付费用户
     */
    @Query("SELECT u FROM IndividualUser u WHERE u.status = 'ACTIVE' AND " +
           "(u.serviceType = 'PREMIUM' OR u.serviceType = 'PRO') AND " +
           "(u.subscriptionExpiresAt IS NULL OR u.subscriptionExpiresAt > :now)")
    List<IndividualUser> findActivePremiumUsers(@Param("now") LocalDateTime now);

    /**
     * 查找订阅即将到期的用户（7天内到期）
     */
    @Query("SELECT u FROM IndividualUser u WHERE u.status = 'ACTIVE' AND " +
           "u.subscriptionExpiresAt BETWEEN :now AND :sevenDaysLater")
    List<IndividualUser> findUsersWithExpiringSoon(@Param("now") LocalDateTime now, 
                                                   @Param("sevenDaysLater") LocalDateTime sevenDaysLater);

    /**
     * 查找需要重置月度评估次数的用户
     */
    @Query("SELECT u FROM IndividualUser u WHERE u.monthlyAssessmentCount > 0 AND " +
           "(u.assessmentCountResetAt IS NULL OR u.assessmentCountResetAt < :lastMonth)")
    List<IndividualUser> findUsersNeedingMonthlyReset(@Param("lastMonth") LocalDateTime lastMonth);

    /**
     * 根据推广渠道码查找用户数量
     */
    @Query("SELECT COUNT(u) FROM IndividualUser u WHERE u.referralCode = :referralCode")
    Long countByReferralCode(@Param("referralCode") String referralCode);

    /**
     * 查找未验证邮箱的用户
     */
    List<IndividualUser> findByEmailVerifiedFalse();

    /**
     * 查找最近注册的用户（按创建时间倒序）
     */
    @Query("SELECT u FROM IndividualUser u WHERE u.createdAt >= :since ORDER BY u.createdAt DESC")
    List<IndividualUser> findRecentRegistrations(@Param("since") LocalDateTime since);

    /**
     * 查找活跃用户（最近30天有登录）
     */
    @Query("SELECT u FROM IndividualUser u WHERE u.status = 'ACTIVE' AND u.lastLoginAt >= :thirtyDaysAgo")
    List<IndividualUser> findActiveUsers(@Param("thirtyDaysAgo") LocalDateTime thirtyDaysAgo);

    /**
     * 统计用户总数
     */
    @Query("SELECT COUNT(u) FROM IndividualUser u WHERE u.status = 'ACTIVE'")
    Long countActiveUsers();

    /**
     * 统计付费用户数
     */
    @Query("SELECT COUNT(u) FROM IndividualUser u WHERE u.status = 'ACTIVE' AND " +
           "(u.serviceType = 'PREMIUM' OR u.serviceType = 'PRO')")
    Long countPremiumUsers();

    /**
     * 根据注册来源统计用户数
     */
    @Query("SELECT u.registrationSource, COUNT(u) FROM IndividualUser u WHERE u.status = 'ACTIVE' " +
           "GROUP BY u.registrationSource")
    List<Object[]> countUsersByRegistrationSource();

    /**
     * 查找高频使用用户（评估次数较多）
     */
    @Query("SELECT u FROM IndividualUser u WHERE u.status = 'ACTIVE' AND u.monthlyAssessmentCount >= :minCount " +
           "ORDER BY u.monthlyAssessmentCount DESC")
    List<IndividualUser> findHighFrequencyUsers(@Param("minCount") Integer minCount);

    /**
     * 软删除用户（更新状态为DELETED）
     */
    @Query("UPDATE IndividualUser u SET u.status = 'DELETED', u.updatedAt = :now WHERE u.id = :userId")
    void softDeleteUser(@Param("userId") UUID userId, @Param("now") LocalDateTime now);

    /**
     * 根据ID和状态查找用户
     */
    Optional<IndividualUser> findByIdAndStatus(UUID id, IndividualUser.AccountStatus status);

    /**
     * 查找今日新注册用户数
     */
    @Query("SELECT COUNT(u) FROM IndividualUser u WHERE DATE(u.createdAt) = DATE(:today)")
    Long countTodayRegistrations(@Param("today") LocalDateTime today);

    /**
     * 查找本月新注册用户数
     */
    @Query("SELECT COUNT(u) FROM IndividualUser u WHERE YEAR(u.createdAt) = :year AND MONTH(u.createdAt) = :month")
    Long countMonthlyRegistrations(@Param("year") int year, @Param("month") int month);
}