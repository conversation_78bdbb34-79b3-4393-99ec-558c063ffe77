name: CI/CD Pipeline - 智能评估平台

on:
  push:
    branches: 
      - main
      - develop
      - feature/*
  pull_request:
    branches: 
      - main
      - develop

env:
  JAVA_VERSION: '21'
  NODE_VERSION: '20'
  MAVEN_OPTS: '-Xmx4g -XX:+UseG1GC'

jobs:
  # 代码质量检查
  code-quality:
    name: 代码质量检查
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout 代码
      uses: actions/checkout@v4
      with:
        fetch-depth: 0  # 全量历史，用于SonarQube分析
    
    - name: 设置 Java ${{ env.JAVA_VERSION }}
      uses: actions/setup-java@v4
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'
        cache: maven
    
    - name: 设置 Node.js ${{ env.NODE_VERSION }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: |
          frontend/uni-app/package-lock.json
          frontend/admin/package-lock.json
    
    - name: Maven 依赖缓存
      uses: actions/cache@v4
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('backend/pom.xml') }}
        restore-keys: ${{ runner.os }}-m2
    
    - name: 后端代码质量检查
      working-directory: ./backend
      run: |
        ./mvnw clean compile checkstyle:check spotbugs:check pmd:check
    
    - name: 前端依赖安装 - uni-app
      working-directory: ./frontend/uni-app
      run: npm ci
    
    - name: 前端依赖安装 - admin
      working-directory: ./frontend/admin
      run: npm ci
    
    - name: 前端代码质量检查 - uni-app
      working-directory: ./frontend/uni-app
      run: |
        npm run lint
        npm run type-check
    
    - name: 前端代码质量检查 - admin
      working-directory: ./frontend/admin
      run: |
        npm run lint
        npm run type-check

  # 后端测试
  backend-tests:
    name: 后端测试套件
    runs-on: ubuntu-latest
    needs: code-quality
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: testpassword
          POSTGRES_USER: testuser
          POSTGRES_DB: assessment_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
    - name: Checkout 代码
      uses: actions/checkout@v4
    
    - name: 设置 Java ${{ env.JAVA_VERSION }}
      uses: actions/setup-java@v4
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'
        cache: maven
    
    - name: 等待数据库就绪
      run: |
        until pg_isready -h localhost -p 5432 -U testuser; do
          echo "等待 PostgreSQL..."
          sleep 2
        done
    
    - name: 运行单元测试
      working-directory: ./backend
      run: |
        ./mvnw clean test -Dspring.profiles.active=test \
          -Dspring.datasource.url=************************************************ \
          -Dspring.datasource.username=testuser \
          -Dspring.datasource.password=testpassword \
          -Dspring.redis.host=localhost \
          -Dspring.redis.port=6379
    
    - name: 生成测试覆盖率报告
      working-directory: ./backend
      run: ./mvnw jacoco:report
    
    - name: 上传覆盖率到 Codecov
      uses: codecov/codecov-action@v4
      with:
        file: ./backend/target/site/jacoco/jacoco.xml
        name: backend-coverage
        fail_ci_if_error: false
    
    - name: 上传测试报告
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: backend-test-reports
        path: |
          backend/target/surefire-reports/
          backend/target/site/jacoco/
        retention-days: 30
    
    - name: 发布测试结果
      uses: dorny/test-reporter@v1
      if: always()
      with:
        name: Backend Tests
        path: backend/target/surefire-reports/*.xml
        reporter: java-junit

  # 前端测试
  frontend-tests:
    name: 前端测试套件
    runs-on: ubuntu-latest
    needs: code-quality
    
    strategy:
      matrix:
        app: [uni-app, admin]
    
    steps:
    - name: Checkout 代码
      uses: actions/checkout@v4
    
    - name: 设置 Node.js ${{ env.NODE_VERSION }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/${{ matrix.app }}/package-lock.json
    
    - name: 安装依赖
      working-directory: ./frontend/${{ matrix.app }}
      run: npm ci
    
    - name: 运行测试
      working-directory: ./frontend/${{ matrix.app }}
      run: npm run test:coverage
    
    - name: 上传覆盖率到 Codecov
      uses: codecov/codecov-action@v4
      with:
        file: ./frontend/${{ matrix.app }}/coverage/lcov.info
        name: frontend-${{ matrix.app }}-coverage
        fail_ci_if_error: false
    
    - name: 上传测试报告
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: frontend-${{ matrix.app }}-test-reports
        path: |
          frontend/${{ matrix.app }}/coverage/
          frontend/${{ matrix.app }}/test-results/
        retention-days: 30

  # 安全扫描
  security-scan:
    name: 安全漏洞扫描
    runs-on: ubuntu-latest
    needs: code-quality
    
    steps:
    - name: Checkout 代码
      uses: actions/checkout@v4
    
    - name: 设置 Java ${{ env.JAVA_VERSION }}
      uses: actions/setup-java@v4
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'
        cache: maven
    
    - name: 运行 OWASP 依赖检查
      working-directory: ./backend
      run: ./mvnw dependency-check:check
    
    - name: 上传 OWASP 报告
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: owasp-dependency-check-report
        path: backend/target/dependency-check-report.html
        retention-days: 30
    
    - name: CodeQL 分析
      uses: github/codeql-action/init@v3
      with:
        languages: java, javascript
    
    - name: 自动构建
      uses: github/codeql-action/autobuild@v3
    
    - name: 执行 CodeQL 分析
      uses: github/codeql-action/analyze@v3

  # 构建应用
  build:
    name: 构建应用镜像
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests]
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop'
    
    steps:
    - name: Checkout 代码
      uses: actions/checkout@v4
    
    - name: 设置 Java ${{ env.JAVA_VERSION }}
      uses: actions/setup-java@v4
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'
        cache: maven
    
    - name: 设置 Node.js ${{ env.NODE_VERSION }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: |
          frontend/uni-app/package-lock.json
          frontend/admin/package-lock.json
    
    - name: 构建后端应用
      working-directory: ./backend
      run: ./mvnw clean package -DskipTests
    
    - name: 构建前端应用 - uni-app
      working-directory: ./frontend/uni-app
      run: |
        npm ci
        npm run build:h5
    
    - name: 构建前端应用 - admin
      working-directory: ./frontend/admin
      run: |
        npm ci
        npm run build
    
    - name: 设置 Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: 登录 Docker Hub
      if: github.event_name != 'pull_request'
      uses: docker/login-action@v3
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}
    
    - name: 构建并推送 Docker 镜像
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./docker/Dockerfile
        platforms: linux/amd64,linux/arm64
        push: ${{ github.event_name != 'pull_request' }}
        tags: |
          ${{ secrets.DOCKER_USERNAME }}/assessment-platform:latest
          ${{ secrets.DOCKER_USERNAME }}/assessment-platform:${{ github.sha }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  # 部署到开发环境
  deploy-dev:
    name: 部署到开发环境
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/develop'
    environment:
      name: development
    
    steps:
    - name: Checkout 代码
      uses: actions/checkout@v4
    
    - name: 部署到开发服务器
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: ${{ secrets.DEV_HOST }}
        username: ${{ secrets.DEV_USERNAME }}
        key: ${{ secrets.DEV_SSH_KEY }}
        script: |
          cd /opt/assessment-platform
          docker-compose pull
          docker-compose up -d --force-recreate
          docker system prune -f

  # 部署到生产环境
  deploy-prod:
    name: 部署到生产环境
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/main'
    environment:
      name: production
    
    steps:
    - name: Checkout 代码
      uses: actions/checkout@v4
    
    - name: 部署到生产服务器
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: ${{ secrets.PROD_HOST }}
        username: ${{ secrets.PROD_USERNAME }}
        key: ${{ secrets.PROD_SSH_KEY }}
        script: |
          cd /opt/assessment-platform
          docker-compose -f docker-compose.prod.yml pull
          docker-compose -f docker-compose.prod.yml up -d --force-recreate
          docker system prune -f

  # 性能测试
  performance-test:
    name: 性能基准测试
    runs-on: ubuntu-latest
    needs: deploy-dev
    if: github.ref == 'refs/heads/develop'
    
    steps:
    - name: Checkout 代码
      uses: actions/checkout@v4
    
    - name: 运行 K6 性能测试
      uses: grafana/k6-action@v0.3.1
      with:
        filename: scripts/performance/load-test.js
      env:
        K6_CLOUD_TOKEN: ${{ secrets.K6_CLOUD_TOKEN }}
        BASE_URL: ${{ secrets.DEV_BASE_URL }}

  # 通知
  notify:
    name: 构建通知
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests, security-scan, build]
    if: always()
    
    steps:
    - name: 发送钉钉通知
      if: ${{ failure() && secrets.DINGTALK_WEBHOOK != '' }}
      run: |
        curl -X POST "${{ secrets.DINGTALK_WEBHOOK }}" \
          -H 'Content-Type: application/json' \
          -d '{
            "msgtype": "text",
            "text": {
              "content": "🚨 CI/CD Pipeline Failed\n\n项目: 智能评估平台\n分支: ${{ github.ref }}\n提交: ${{ github.sha }}\n状态: 构建失败 ❌\n详情: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
            }
          }'
    
    - name: 发送成功通知
      if: ${{ success() && secrets.DINGTALK_WEBHOOK != '' }}
      run: |
        curl -X POST "${{ secrets.DINGTALK_WEBHOOK }}" \
          -H 'Content-Type: application/json' \
          -d '{
            "msgtype": "text",
            "text": {
              "content": "✅ CI/CD Pipeline Success\n\n项目: 智能评估平台\n分支: ${{ github.ref }}\n提交: ${{ github.sha }}\n状态: 构建成功 ✅\n详情: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
            }
          }'