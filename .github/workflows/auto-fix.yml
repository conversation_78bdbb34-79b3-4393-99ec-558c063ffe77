name: 🤖 自动代码修复

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

permissions:
  contents: write
  pull-requests: write

jobs:
  auto-fix:
    runs-on: ubuntu-latest
    if: "!contains(github.event.head_commit.message, '[skip-autofix]')"
    
    steps:
      - name: 📥 检出代码
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          fetch-depth: 0
          
      - name: 🟢 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
          
      - name: ☕ 设置 Java
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: '21'
          
      - name: 🎨 修复前端代码格式 (uni-app)
        run: |
          if [ -d "frontend/uni-app" ]; then
            cd frontend/uni-app
            if [ -f "package.json" ]; then
              npm ci --silent || npm install --silent
              # ESLint 自动修复
              npm run lint:fix 2>/dev/null || npx eslint . --fix --ext .js,.ts,.vue || true
              # Prettier 格式化
              npm run format 2>/dev/null || npx prettier --write "**/*.{js,ts,vue,json}" || true
            fi
          fi
          
      - name: 🎨 修复前端代码格式 (admin)
        run: |
          if [ -d "frontend/admin" ]; then
            cd frontend/admin
            if [ -f "package.json" ]; then
              npm ci --silent || npm install --silent
              # ESLint 自动修复
              npm run lint:fix 2>/dev/null || npx eslint . --fix --ext .js,.ts,.vue || true
              # Prettier 格式化  
              npm run format 2>/dev/null || npx prettier --write "**/*.{js,ts,vue,json}" || true
            fi
          fi
          
      - name: ☕ 修复 Java 代码格式
        run: |
          if [ -d "backend" ]; then
            cd backend
            # Google Java Format (如果配置了)
            if [ -f "pom.xml" ]; then
              # 检查是否有格式化插件配置
              if grep -q "google-java-format" pom.xml; then
                ./mvnw com.coveo:fmt-maven-plugin:format || true
              fi
              # Spotless 格式化 (如果配置了)
              if grep -q "spotless" pom.xml; then
                ./mvnw spotless:apply || true
              fi
            fi
          fi
          
      - name: 📝 修复 Markdown 格式
        run: |
          # Prettier 格式化 Markdown
          npx prettier --write "**/*.md" --prose-wrap=preserve || true
          
      - name: 🔧 修复 YAML 格式
        run: |
          # 修复 GitHub workflows
          npx prettier --write ".github/workflows/*.yml" || true
          
      - name: 📊 检查更改
        id: changes
        run: |
          git add .
          if git diff --staged --quiet; then
            echo "has_changes=false" >> $GITHUB_OUTPUT
            echo "ℹ️ 没有需要自动修复的代码格式问题"
          else
            echo "has_changes=true" >> $GITHUB_OUTPUT
            echo "🔧 发现并修复了代码格式问题"
            git status --porcelain
          fi
          
      - name: 💾 提交修复
        if: steps.changes.outputs.has_changes == 'true'
        run: |
          git config --local user.email "41898282+github-actions[bot]@users.noreply.github.com"
          git config --local user.name "github-actions[bot]"
          
          # 创建详细的提交信息
          echo "🤖 自动修复代码格式问题" > commit_msg.tmp
          echo "" >> commit_msg.tmp
          echo "自动修复内容:" >> commit_msg.tmp
          echo "- ESLint 规则修复" >> commit_msg.tmp
          echo "- Prettier 格式化" >> commit_msg.tmp
          echo "- Java 代码格式化" >> commit_msg.tmp
          echo "- Markdown/YAML 格式化" >> commit_msg.tmp
          echo "" >> commit_msg.tmp
          echo "🚀 Generated with GitHub Actions" >> commit_msg.tmp
          
          git commit -F commit_msg.tmp
          rm commit_msg.tmp
          
      - name: 📤 推送修复
        if: steps.changes.outputs.has_changes == 'true' && github.event_name == 'push'
        run: |
          git push
          
      - name: 💬 评论 PR (如果是 Pull Request)
        if: steps.changes.outputs.has_changes == 'true' && github.event_name == 'pull_request'
        uses: actions/github-script@v7
        with:
          script: |
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: `🤖 **自动代码修复完成**
              
              检测到代码格式问题并已自动修复：
              - ✅ ESLint 规则修复
              - ✅ Prettier 格式化
              - ✅ Java 代码格式化
              - ✅ 配置文件格式化
              
              请重新拉取代码查看修复结果。如需跳过自动修复，请在提交信息中添加 \`[skip-autofix]\`。`
            })

  # 只在修复后重新运行质量检查
  recheck-after-fix:
    needs: auto-fix
    if: needs.auto-fix.outputs.has_changes == 'true'
    runs-on: ubuntu-latest
    
    steps:
      - name: 📥 检出修复后的代码
        uses: actions/checkout@v4
        
      - name: 🔍 重新运行代码质量检查
        run: |
          echo "🔄 重新运行质量检查以验证修复结果..."
          # 这里会触发其他 workflow 重新检查