name: Dependency Review - 依赖审查

on:
  # 每次有依赖更新 PR 时触发
  pull_request:
    paths:
      - 'backend/pom.xml'
      - 'frontend/uni-app/package.json'
      - 'frontend/admin/package.json'
      - 'frontend/*/package-lock.json'
  
  # 每周二早上进行依赖安全审查
  schedule:
    - cron: '0 8 * * 2'
  
  workflow_dispatch:

env:
  JAVA_VERSION: '21'
  NODE_VERSION: '20'

jobs:
  dependency-analysis:
    name: 依赖变更分析
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    
    steps:
    - name: Checkout PR branch
      uses: actions/checkout@v4
      with:
        ref: ${{ github.event.pull_request.head.sha }}
        fetch-depth: 0
    
    - name: Checkout base branch
      run: |
        git fetch origin ${{ github.event.pull_request.base.ref }}
        git checkout ${{ github.event.pull_request.base.ref }}
        cp backend/pom.xml backend/pom.xml.base
        cp frontend/uni-app/package.json frontend/uni-app/package.json.base || true
        cp frontend/admin/package.json frontend/admin/package.json.base || true
        git checkout ${{ github.event.pull_request.head.ref }}
    
    - name: 设置 Java ${{ env.JAVA_VERSION }}
      uses: actions/setup-java@v4
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'
        cache: maven
    
    - name: 设置 Node.js ${{ env.NODE_VERSION }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
    
    - name: 分析后端依赖变更
      working-directory: ./backend
      run: |
        echo "# 后端依赖变更分析" > ../dependency-analysis.md
        echo "" >> ../dependency-analysis.md
        echo "## 📦 Maven 依赖变更" >> ../dependency-analysis.md
        echo "" >> ../dependency-analysis.md
        
        if [ -f "pom.xml.base" ]; then
          echo "### 变更对比" >> ../dependency-analysis.md
          echo "\`\`\`diff" >> ../dependency-analysis.md
          diff -u pom.xml.base pom.xml >> ../dependency-analysis.md || true
          echo "\`\`\`" >> ../dependency-analysis.md
          echo "" >> ../dependency-analysis.md
          
          # 检查版本变更
          echo "### 版本升级详情" >> ../dependency-analysis.md
          echo "" >> ../dependency-analysis.md
          
          # 提取依赖版本信息
          ./mvnw dependency:list -DoutputFile=current-deps.txt -Dsilent=true
          cp pom.xml.base pom.xml.temp
          cp pom.xml.base pom.xml
          ./mvnw dependency:list -DoutputFile=base-deps.txt -Dsilent=true
          cp pom.xml.temp pom.xml
          
          # 分析版本变更
          echo "| 依赖 | 旧版本 | 新版本 | 变更类型 |" >> ../dependency-analysis.md
          echo "|------|--------|--------|----------|" >> ../dependency-analysis.md
          
          # 这里可以添加更复杂的版本比较逻辑
          echo "请手动检查 pom.xml 中的版本变更" >> ../dependency-analysis.md
        else
          echo "无法找到基础版本进行比较" >> ../dependency-analysis.md
        fi
        
        echo "" >> ../dependency-analysis.md
        echo "### 🔍 安全检查" >> ../dependency-analysis.md
        echo "" >> ../dependency-analysis.md
        
        # 运行依赖安全检查
        ./mvnw dependency-check:check -DfailBuildOnCVSS=7 || echo "发现安全漏洞，请查看详细报告"
        
        if [ -f "target/dependency-check-report.xml" ]; then
          vulnerabilities=$(grep -o '<vulnerability ' target/dependency-check-report.xml | wc -l)
          if [ $vulnerabilities -gt 0 ]; then
            echo "⚠️ **发现 $vulnerabilities 个安全漏洞**" >> ../dependency-analysis.md
            echo "" >> ../dependency-analysis.md
            echo "请查看 [dependency-check 报告](./target/dependency-check-report.html)" >> ../dependency-analysis.md
          else
            echo "✅ 未发现安全漏洞" >> ../dependency-analysis.md
          fi
        fi
    
    - name: 分析前端依赖变更 - uni-app
      working-directory: ./frontend/uni-app
      run: |
        echo "" >> ../../dependency-analysis.md
        echo "## 📱 uni-app 依赖变更" >> ../../dependency-analysis.md
        echo "" >> ../../dependency-analysis.md
        
        if [ -f "package.json.base" ]; then
          echo "### 变更对比" >> ../../dependency-analysis.md
          echo "\`\`\`diff" >> ../../dependency-analysis.md
          diff -u package.json.base package.json >> ../../dependency-analysis.md || true
          echo "\`\`\`" >> ../../dependency-analysis.md
          echo "" >> ../../dependency-analysis.md
          
          # NPM audit 安全检查
          npm install --silent
          npm audit --json > audit-result.json 2>/dev/null || true
          
          if [ -f "audit-result.json" ]; then
            vulnerabilities=$(jq '.metadata.vulnerabilities.total // 0' audit-result.json)
            if [ "$vulnerabilities" -gt 0 ]; then
              echo "⚠️ **发现 $vulnerabilities 个前端安全漏洞**" >> ../../dependency-analysis.md
              echo "" >> ../../dependency-analysis.md
              echo "- 高危: $(jq '.metadata.vulnerabilities.high // 0' audit-result.json)" >> ../../dependency-analysis.md
              echo "- 中危: $(jq '.metadata.vulnerabilities.moderate // 0' audit-result.json)" >> ../../dependency-analysis.md
              echo "- 低危: $(jq '.metadata.vulnerabilities.low // 0' audit-result.json)" >> ../../dependency-analysis.md
            else
              echo "✅ 前端依赖无安全漏洞" >> ../../dependency-analysis.md
            fi
          fi
        else
          echo "无法找到基础版本进行比较" >> ../../dependency-analysis.md
        fi
    
    - name: 分析前端依赖变更 - admin
      working-directory: ./frontend/admin
      run: |
        echo "" >> ../../dependency-analysis.md
        echo "## 🖥️ admin 依赖变更" >> ../../dependency-analysis.md
        echo "" >> ../../dependency-analysis.md
        
        if [ -f "package.json.base" ]; then
          echo "### 变更对比" >> ../../dependency-analysis.md
          echo "\`\`\`diff" >> ../../dependency-analysis.md
          diff -u package.json.base package.json >> ../../dependency-analysis.md || true
          echo "\`\`\`" >> ../../dependency-analysis.md
          echo "" >> ../../dependency-analysis.md
          
          # NPM audit 安全检查
          npm install --silent
          npm audit --json > audit-result.json 2>/dev/null || true
          
          if [ -f "audit-result.json" ]; then
            vulnerabilities=$(jq '.metadata.vulnerabilities.total // 0' audit-result.json)
            if [ "$vulnerabilities" -gt 0 ]; then
              echo "⚠️ **发现 $vulnerabilities 个管理后台安全漏洞**" >> ../../dependency-analysis.md
              echo "" >> ../../dependency-analysis.md
              echo "- 高危: $(jq '.metadata.vulnerabilities.high // 0' audit-result.json)" >> ../../dependency-analysis.md
              echo "- 中危: $(jq '.metadata.vulnerabilities.moderate // 0' audit-result.json)" >> ../../dependency-analysis.md
              echo "- 低危: $(jq '.metadata.vulnerabilities.low // 0' audit-result.json)" >> ../../dependency-analysis.md
            else
              echo "✅ 管理后台依赖无安全漏洞" >> ../../dependency-analysis.md
            fi
          fi
        else
          echo "无法找到基础版本进行比较" >> ../../dependency-analysis.md
        fi
    
    - name: 生成审查建议
      run: |
        echo "" >> dependency-analysis.md
        echo "## 🔍 审查建议" >> dependency-analysis.md
        echo "" >> dependency-analysis.md
        echo "### ✅ 审查清单" >> dependency-analysis.md
        echo "" >> dependency-analysis.md
        echo "- [ ] **安全性**: 检查是否引入新的安全漏洞" >> dependency-analysis.md
        echo "- [ ] **兼容性**: 验证新版本与现有代码的兼容性" >> dependency-analysis.md
        echo "- [ ] **稳定性**: 确认升级版本的稳定性和成熟度" >> dependency-analysis.md
        echo "- [ ] **许可证**: 检查新依赖的许可证是否符合项目要求" >> dependency-analysis.md
        echo "- [ ] **性能影响**: 评估版本升级对性能的潜在影响" >> dependency-analysis.md
        echo "- [ ] **测试覆盖**: 确保有足够的测试覆盖升级的功能" >> dependency-analysis.md
        echo "" >> dependency-analysis.md
        echo "### 🚀 测试验证" >> dependency-analysis.md
        echo "1. 运行完整测试套件" >> dependency-analysis.md
        echo "2. 执行手动回归测试" >> dependency-analysis.md
        echo "3. 检查构建和部署流程" >> dependency-analysis.md
        echo "4. 验证关键业务功能" >> dependency-analysis.md
        echo "" >> dependency-analysis.md
        echo "### 📝 发布说明" >> dependency-analysis.md
        echo "如果此PR被合并，请在发布说明中包含:" >> dependency-analysis.md
        echo "- 升级的主要依赖及版本" >> dependency-analysis.md
        echo "- 潜在的破坏性变更" >> dependency-analysis.md
        echo "- 新功能或改进" >> dependency-analysis.md
        echo "- 安全修复详情" >> dependency-analysis.md
    
    - name: 添加 PR 评论
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');
          
          try {
            const analysisContent = fs.readFileSync('dependency-analysis.md', 'utf8');
            
            // 查找现有的依赖分析评论
            const comments = await github.rest.issues.listComments({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number,
            });
            
            const botComment = comments.data.find(comment => 
              comment.user.type === 'Bot' && 
              comment.body.includes('依赖变更分析')
            );
            
            const commentBody = `## 🔍 依赖变更分析\n\n${analysisContent}\n\n---\n*此分析由 GitHub Actions 自动生成*`;
            
            if (botComment) {
              // 更新现有评论
              await github.rest.issues.updateComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                comment_id: botComment.id,
                body: commentBody
              });
            } else {
              // 创建新评论
              await github.rest.issues.createComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: context.issue.number,
                body: commentBody
              });
            }
          } catch (error) {
            console.error('Error processing dependency analysis:', error);
          }
    
    - name: 上传分析报告
      uses: actions/upload-artifact@v4
      with:
        name: dependency-analysis-report
        path: |
          dependency-analysis.md
          backend/target/dependency-check-report.*
          frontend/uni-app/audit-result.json
          frontend/admin/audit-result.json
        retention-days: 30

  dependency-health-check:
    name: 依赖健康度检查
    runs-on: ubuntu-latest
    if: github.event_name == 'schedule' || github.event_name == 'workflow_dispatch'
    
    steps:
    - name: Checkout 代码
      uses: actions/checkout@v4
    
    - name: 设置 Java ${{ env.JAVA_VERSION }}
      uses: actions/setup-java@v4
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'
        cache: maven
    
    - name: 设置 Node.js ${{ env.NODE_VERSION }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
    
    - name: 检查过期依赖
      run: |
        echo "# 依赖健康度报告" > dependency-health.md
        echo "" >> dependency-health.md
        echo "**检查时间**: $(date)" >> dependency-health.md
        echo "" >> dependency-health.md
        
        # 后端依赖检查
        echo "## 🍃 后端依赖 (Maven)" >> dependency-health.md
        echo "" >> dependency-health.md
        
        cd backend
        ./mvnw versions:display-dependency-updates | tee ../backend-updates.txt
        
        echo "### 可更新的依赖" >> ../dependency-health.md
        echo "\`\`\`" >> ../dependency-health.md
        grep -A 20 "The following dependencies in Dependencies have newer versions:" ../backend-updates.txt >> ../dependency-health.md || echo "所有依赖都是最新版本" >> ../dependency-health.md
        echo "\`\`\`" >> ../dependency-health.md
        cd ..
        
        # 前端依赖检查 - uni-app
        echo "" >> dependency-health.md
        echo "## 📱 uni-app 依赖 (NPM)" >> dependency-health.md
        echo "" >> dependency-health.md
        
        cd frontend/uni-app
        npm ci
        npx npm-check-updates > ../../uniapp-updates.txt || true
        
        echo "### 可更新的依赖" >> ../../dependency-health.md
        echo "\`\`\`" >> ../../dependency-health.md
        head -50 ../../uniapp-updates.txt >> ../../dependency-health.md
        echo "\`\`\`" >> ../../dependency-health.md
        cd ../..
        
        # 前端依赖检查 - admin
        echo "" >> dependency-health.md
        echo "## 🖥️ admin 依赖 (NPM)" >> dependency-health.md
        echo "" >> dependency-health.md
        
        cd frontend/admin
        npm ci
        npx npm-check-updates > ../../admin-updates.txt || true
        
        echo "### 可更新的依赖" >> ../../dependency-health.md
        echo "\`\`\`" >> ../../dependency-health.md
        head -50 ../../admin-updates.txt >> ../../dependency-health.md
        echo "\`\`\`" >> ../../dependency-health.md
        cd ../..
    
    - name: 生成依赖更新建议
      run: |
        echo "" >> dependency-health.md
        echo "## 🎯 更新建议" >> dependency-health.md
        echo "" >> dependency-health.md
        
        # 检查是否有安全更新
        cd backend
        ./mvnw dependency-check:check -DfailBuildOnCVSS=10 || true
        if [ -f "target/dependency-check-report.xml" ]; then
          vulnerabilities=$(grep -o '<vulnerability ' target/dependency-check-report.xml | wc -l)
          if [ $vulnerabilities -gt 0 ]; then
            echo "🚨 **紧急**: 发现 $vulnerabilities 个安全漏洞，建议立即更新相关依赖" >> ../dependency-health.md
          fi
        fi
        cd ..
        
        echo "" >> dependency-health.md
        echo "### 📋 更新优先级" >> dependency-health.md
        echo "1. **高优先级**: 安全漏洞修复" >> dependency-health.md
        echo "2. **中优先级**: 重要功能改进和bug修复" >> dependency-health.md  
        echo "3. **低优先级**: 次要版本升级" >> dependency-health.md
        echo "" >> dependency-health.md
        echo "### 🔄 建议的更新策略" >> dependency-health.md
        echo "- 每周执行 patch 版本更新" >> dependency-health.md
        echo "- 每月执行 minor 版本更新" >> dependency-health.md
        echo "- 每季度评估 major 版本更新" >> dependency-health.md
    
    - name: 创建或更新 Issue
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');
          const healthContent = fs.readFileSync('dependency-health.md', 'utf8');
          
          const title = `📦 依赖健康度检查 - ${new Date().toISOString().split('T')[0]}`;
          
          // 查找现有的健康度检查 Issue
          const issues = await github.rest.issues.listForRepo({
            owner: context.repo.owner,
            repo: context.repo.repo,
            labels: 'dependency-health',
            state: 'open'
          });
          
          const existingIssue = issues.data.find(issue => 
            issue.title.includes('依赖健康度检查')
          );
          
          const issueBody = `${healthContent}\n\n---\n*此报告由 GitHub Actions 自动生成*`;
          
          if (existingIssue) {
            // 更新现有 Issue
            await github.rest.issues.update({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: existingIssue.number,
              title: title,
              body: issueBody
            });
          } else {
            // 创建新 Issue
            await github.rest.issues.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              title: title,
              body: issueBody,
              labels: ['dependency-health', 'maintenance']
            });
          }
    
    - name: 上传健康度报告
      uses: actions/upload-artifact@v4
      with:
        name: dependency-health-report
        path: |
          dependency-health.md
          backend-updates.txt
          uniapp-updates.txt
          admin-updates.txt
        retention-days: 90