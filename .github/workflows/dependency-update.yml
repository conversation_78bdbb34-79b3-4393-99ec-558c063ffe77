name: Dependency Update - 依赖更新

on:
  schedule:
    # 每周五下午3点检查依赖更新
    - cron: '0 15 * * 5'
  workflow_dispatch:
    inputs:
      update_type:
        description: '更新类型'
        required: true
        default: 'minor'
        type: choice
        options:
        - patch
        - minor
        - major

env:
  JAVA_VERSION: '21'
  NODE_VERSION: '20'

jobs:
  update-backend-dependencies:
    name: 更新后端依赖
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout 代码
      uses: actions/checkout@v4
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
    
    - name: 设置 Java ${{ env.JAVA_VERSION }}
      uses: actions/setup-java@v4
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'
        cache: maven
    
    - name: 检查可更新的依赖
      working-directory: ./backend
      run: |
        ./mvnw versions:display-dependency-updates > dependency-updates.txt
        ./mvnw versions:display-plugin-updates >> dependency-updates.txt
        cat dependency-updates.txt
    
    - name: 更新依赖版本 (patch)
      if: github.event.inputs.update_type == 'patch' || github.event.inputs.update_type == ''
      working-directory: ./backend
      run: |
        # 更新patch版本的依赖
        ./mvnw versions:use-latest-versions -DallowMajorUpdates=false -DallowMinorUpdates=false
        ./mvnw versions:use-latest-versions -Dincludes="*:*:*:*:*" -DallowMajorUpdates=false -DallowMinorUpdates=false
    
    - name: 更新依赖版本 (minor)
      if: github.event.inputs.update_type == 'minor'
      working-directory: ./backend
      run: |
        # 更新minor版本的依赖
        ./mvnw versions:use-latest-versions -DallowMajorUpdates=false
    
    - name: 更新依赖版本 (major)
      if: github.event.inputs.update_type == 'major'
      working-directory: ./backend
      run: |
        # 更新所有版本的依赖（谨慎使用）
        ./mvnw versions:use-latest-versions
    
    - name: 清理版本备份文件
      working-directory: ./backend
      run: |
        find . -name "pom.xml.versionsBackup" -delete
    
    - name: 运行测试验证
      working-directory: ./backend
      run: |
        ./mvnw clean test -DskipTests=false -Dtest="HealthControllerTest,AssessmentServiceTest" || echo "Some tests failed, manual review needed"
    
    - name: 生成依赖更新报告
      working-directory: ./backend
      run: |
        echo "# 后端依赖更新报告" > ../dependency-update-backend.md
        echo "" >> ../dependency-update-backend.md
        echo "**更新时间**: $(date)" >> ../dependency-update-backend.md
        echo "**更新类型**: ${{ github.event.inputs.update_type || 'patch' }}" >> ../dependency-update-backend.md
        echo "" >> ../dependency-update-backend.md
        
        if git diff --name-only | grep -q "pom.xml"; then
          echo "## 📦 已更新的依赖" >> ../dependency-update-backend.md
          echo "" >> ../dependency-update-backend.md
          echo "\`\`\`diff" >> ../dependency-update-backend.md
          git diff pom.xml >> ../dependency-update-backend.md
          echo "\`\`\`" >> ../dependency-update-backend.md
          echo "" >> ../dependency-update-backend.md
          
          echo "## 🔍 变更详情" >> ../dependency-update-backend.md
          echo "- 查看具体变更请参考上方diff" >> ../dependency-update-backend.md
          echo "- 建议进行充分测试后合并" >> ../dependency-update-backend.md
        else
          echo "## ℹ️ 无依赖更新" >> ../dependency-update-backend.md
          echo "所有依赖都是最新版本" >> ../dependency-update-backend.md
        fi
    
    - name: 上传后端更新报告
      uses: actions/upload-artifact@v4
      with:
        name: backend-dependency-update-report
        path: dependency-update-backend.md
        retention-days: 30
    
    outputs:
      backend-updated: ${{ steps.check-changes.outputs.backend-updated }}

  update-frontend-dependencies:
    name: 更新前端依赖
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        app: [uni-app, admin]
    
    steps:
    - name: Checkout 代码
      uses: actions/checkout@v4
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
    
    - name: 设置 Node.js ${{ env.NODE_VERSION }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/${{ matrix.app }}/package-lock.json
    
    - name: 检查可更新的依赖
      working-directory: ./frontend/${{ matrix.app }}
      run: |
        npm outdated > outdated-packages.txt || true
        cat outdated-packages.txt
    
    - name: 更新依赖 (patch)
      if: github.event.inputs.update_type == 'patch' || github.event.inputs.update_type == ''
      working-directory: ./frontend/${{ matrix.app }}
      run: |
        # 只更新patch版本
        npx npm-check-updates --target patch --upgrade
        npm install
    
    - name: 更新依赖 (minor)
      if: github.event.inputs.update_type == 'minor'
      working-directory: ./frontend/${{ matrix.app }}
      run: |
        # 更新minor版本
        npx npm-check-updates --target minor --upgrade
        npm install
    
    - name: 更新依赖 (major)
      if: github.event.inputs.update_type == 'major'
      working-directory: ./frontend/${{ matrix.app }}
      run: |
        # 更新所有版本（谨慎使用）
        npx npm-check-updates --upgrade
        npm install
    
    - name: 运行基本测试
      working-directory: ./frontend/${{ matrix.app }}
      run: |
        npm run lint || echo "Linting failed, manual review needed"
        npm run type-check || echo "Type check failed, manual review needed"
    
    - name: 生成依赖更新报告
      working-directory: ./frontend/${{ matrix.app }}
      run: |
        echo "# 前端依赖更新报告 - ${{ matrix.app }}" > ../../dependency-update-${{ matrix.app }}.md
        echo "" >> ../../dependency-update-${{ matrix.app }}.md
        echo "**更新时间**: $(date)" >> ../../dependency-update-${{ matrix.app }}.md
        echo "**更新类型**: ${{ github.event.inputs.update_type || 'patch' }}" >> ../../dependency-update-${{ matrix.app }}.md
        echo "" >> ../../dependency-update-${{ matrix.app }}.md
        
        if git diff --name-only | grep -q "package.json\|package-lock.json"; then
          echo "## 📦 已更新的依赖" >> ../../dependency-update-${{ matrix.app }}.md
          echo "" >> ../../dependency-update-${{ matrix.app }}.md
          echo "### package.json 变更" >> ../../dependency-update-${{ matrix.app }}.md
          echo "\`\`\`diff" >> ../../dependency-update-${{ matrix.app }}.md
          git diff package.json >> ../../dependency-update-${{ matrix.app }}.md || echo "No package.json changes" >> ../../dependency-update-${{ matrix.app }}.md
          echo "\`\`\`" >> ../../dependency-update-${{ matrix.app }}.md
          echo "" >> ../../dependency-update-${{ matrix.app }}.md
          
          echo "## 🔍 变更详情" >> ../../dependency-update-${{ matrix.app }}.md
          echo "- 查看具体变更请参考上方diff" >> ../../dependency-update-${{ matrix.app }}.md
          echo "- 建议进行充分测试后合并" >> ../../dependency-update-${{ matrix.app }}.md
        else
          echo "## ℹ️ 无依赖更新" >> ../../dependency-update-${{ matrix.app }}.md
          echo "所有依赖都是最新版本" >> ../../dependency-update-${{ matrix.app }}.md
        fi
    
    - name: 上传前端更新报告
      uses: actions/upload-artifact@v4
      with:
        name: frontend-${{ matrix.app }}-dependency-update-report
        path: dependency-update-${{ matrix.app }}.md
        retention-days: 30

  create-update-pr:
    name: 创建依赖更新PR
    runs-on: ubuntu-latest
    needs: [update-backend-dependencies, update-frontend-dependencies]
    if: always()
    
    steps:
    - name: Checkout 代码
      uses: actions/checkout@v4
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        fetch-depth: 0
    
    - name: 重新应用所有更改
      run: |
        # 重新运行依赖更新（因为每个job都是独立的环境）
        echo "Note: In a real scenario, we would persist changes between jobs"
        echo "This is a placeholder for creating the actual PR with combined changes"
    
    - name: 检查是否有变更
      id: check-changes
      run: |
        if git diff --quiet HEAD^; then
          echo "has-changes=false" >> $GITHUB_OUTPUT
        else
          echo "has-changes=true" >> $GITHUB_OUTPUT
        fi
    
    - name: 创建或更新依赖更新分支
      if: steps.check-changes.outputs.has-changes == 'true'
      run: |
        branch_name="dependency-update-$(date +%Y-%m-%d)"
        git checkout -b $branch_name
        
        # 提交所有依赖更新
        git add .
        git config user.name "github-actions[bot]"
        git config user.email "github-actions[bot]@users.noreply.github.com"
        git commit -m "chore: update dependencies (${{ github.event.inputs.update_type || 'patch' }} updates)

        🤖 Generated with [Claude Code](https://claude.ai/code)
        
        Co-Authored-By: Claude <<EMAIL>>"
        
        git push origin $branch_name
        
        echo "BRANCH_NAME=$branch_name" >> $GITHUB_ENV
    
    - name: 下载所有更新报告
      if: steps.check-changes.outputs.has-changes == 'true'
      uses: actions/download-artifact@v4
      with:
        path: ./dependency-reports
    
    - name: 创建 Pull Request
      if: steps.check-changes.outputs.has-changes == 'true'
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');
          
          let prBody = `## 📦 依赖更新\n\n`;
          prBody += `**更新类型**: ${{ github.event.inputs.update_type || 'patch' }}\n`;
          prBody += `**更新时间**: ${new Date().toISOString()}\n\n`;
          
          prBody += `### 🔍 本次更新包含:\n\n`;
          
          // 尝试读取各个更新报告
          try {
            const backendReport = fs.readFileSync('./dependency-reports/backend-dependency-update-report/dependency-update-backend.md', 'utf8');
            prBody += `#### 后端 (Java/Maven)\n${backendReport}\n\n`;
          } catch (e) {
            prBody += `#### 后端 (Java/Maven)\n无可用更新报告\n\n`;
          }
          
          try {
            const uniAppReport = fs.readFileSync('./dependency-reports/frontend-uni-app-dependency-update-report/dependency-update-uni-app.md', 'utf8');
            prBody += `#### 前端 uni-app\n${uniAppReport}\n\n`;
          } catch (e) {
            prBody += `#### 前端 uni-app\n无可用更新报告\n\n`;
          }
          
          try {
            const adminReport = fs.readFileSync('./dependency-reports/frontend-admin-dependency-update-report/dependency-update-admin.md', 'utf8');
            prBody += `#### 前端 admin\n${adminReport}\n\n`;
          } catch (e) {
            prBody += `#### 前端 admin\n无可用更新报告\n\n`;
          }
          
          prBody += `### ✅ 验证清单\n\n`;
          prBody += `- [ ] 后端单元测试通过\n`;
          prBody += `- [ ] 前端类型检查通过\n`;
          prBody += `- [ ] 前端linting通过\n`;
          prBody += `- [ ] 手动功能测试完成\n`;
          prBody += `- [ ] 安全扫描无新增高危漏洞\n\n`;
          
          prBody += `### 🤖 自动化信息\n\n`;
          prBody += `- 由 GitHub Actions 自动创建\n`;
          prBody += `- 分支: \`${process.env.BRANCH_NAME}\`\n`;
          prBody += `- 工作流运行: [#${{ github.run_number }}](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }})\n\n`;
          
          prBody += `🤖 Generated with [Claude Code](https://claude.ai/code)\n\n`;
          prBody += `Co-Authored-By: Claude <<EMAIL>>`;
          
          await github.rest.pulls.create({
            owner: context.repo.owner,
            repo: context.repo.repo,
            title: `🔄 Dependencies Update - ${{ github.event.inputs.update_type || 'patch' }} (${new Date().toISOString().split('T')[0]})`,
            head: process.env.BRANCH_NAME,
            base: 'main',
            body: prBody,
            draft: false
          });
    
    - name: 发送依赖更新通知
      if: ${{ always() && secrets.DINGTALK_WEBHOOK != '' }}
      run: |
        curl -X POST "${{ secrets.DINGTALK_WEBHOOK }}" \
          -H 'Content-Type: application/json' \
          -d '{
            "msgtype": "text",
            "text": {
              "content": "📦 智能评估平台依赖更新\n\n**更新类型**: ${{ github.event.inputs.update_type || '"patch"' }}\n**时间**: $(date)\n**状态**: ${{ job.status }}\n\n${{ steps.check-changes.outputs.has-changes == '"true"' && '🔄 已创建依赖更新PR，请及时review' || 'ℹ️ 所有依赖都是最新版本' }}\n\n🔗 链接: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
            }
          }'