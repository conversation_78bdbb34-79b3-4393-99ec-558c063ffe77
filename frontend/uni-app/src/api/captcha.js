import { request } from '@/utils/request'

/**
 * 验证码相关API
 */

/**
 * 获取滑动验证码
 * @returns {Promise} 验证码数据
 */
export function getCaptcha() {
  return request({
    url: '/api/captcha/get',
    method: 'GET'
  })
}

/**
 * 校验滑动验证码
 * @param {Object} data 验证数据
 * @param {string} data.captchaType 验证码类型 (blockPuzzle/clickWord)
 * @param {string} data.token 验证码token
 * @param {string} data.pointJson 滑动坐标JSON字符串
 * @param {string} data.verification 验证密钥
 * @returns {Promise} 校验结果
 */
export function checkCaptcha(data) {
  return request({
    url: '/api/captcha/check',
    method: 'POST',
    data
  })
}

/**
 * 验证码二次验证（用于登录）
 * @param {Object} data 验证数据
 * @param {string} data.captchaType 验证码类型
 * @param {string} data.token 验证码token
 * @param {string} data.verification 验证密钥
 * @returns {Promise} 验证结果
 */
export function verifyCaptcha(data) {
  return request({
    url: '/api/captcha/verify',
    method: 'POST',
    data
  })
}