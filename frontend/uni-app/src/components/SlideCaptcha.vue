<template>
  <div class="slide-captcha" data-env="uniapp">
    <!-- 验证码状态提示 -->
    <div class="captcha-status" v-if="verified">
      <span class="status-icon">✅</span>
      <span class="status-text">滑动验证码已通过</span>
    </div>
    <div class="captcha-status warning" v-else>
      <span class="status-icon">🔒</span>
      <span class="status-text">请完成下方滑动验证码</span>
    </div>

    <!-- 内嵌滑动验证码 -->
    <div v-if="!verified && captchaData?.originalImageBase64" class="inline-captcha">
      <!-- 背景图片容器 -->
      <div class="captcha-image-panel" :style="{ width: imgWidth + 'rpx', height: imgHeight + 'rpx' }">
        <!-- 背景图片 -->
        <img
          :src="'data:image/png;base64,' + (captchaData?.originalImageBase64 || '')"
          class="captcha-bg-image"
          draggable="false"
        />

        <!-- 滑块图片 -->
        <div
          v-if="captchaData?.jigsawImageBase64"
          class="captcha-block"
          :style="{ left: blockLeft + 'rpx', top: '0rpx', width: blockWidth + 'rpx', height: imgHeight + 'rpx' }"
        >
          <img
            :src="'data:image/png;base64,' + (captchaData?.jigsawImageBase64 || '')"
            class="captcha-block-image"
            draggable="false"
          />
        </div>

        <!-- 刷新按钮 -->
        <div class="captcha-refresh" @click="refreshCaptcha">
          <span class="refresh-icon">🔄</span>
        </div>

        <!-- 提示信息 -->
        <div v-if="tipMessage" class="captcha-tip" :class="verifySuccess ? 'tip-success' : 'tip-error'">
          {{ tipMessage }}
        </div>
      </div>

      <!-- 滑动条 -->
      <div class="captcha-slider" :style="{ width: imgWidth + 'rpx', height: '80rpx' }">
        <!-- 滑动轨道 -->
        <div class="slider-track">
          <div class="slider-track-bg">
            <span class="slider-text">{{ sliderText }}</span>
          </div>

          <!-- 已滑动区域 -->
          <div class="slider-fill" :style="{ width: sliderLeft + 'rpx' }">
            <span class="slider-text">{{ finishText }}</span>
          </div>
        </div>

        <!-- 滑块 -->
        <div
          class="slider-button"
          :style="{ left: sliderLeft + 'rpx', width: '80rpx', height: '80rpx' }"
          @touchstart="handleTouchStart"
          @touchmove="handleTouchMove"
          @touchend="handleTouchEnd"
        >
          <span class="slider-icon" :class="verifySuccess ? 'icon-success' : 'icon-normal'">
            {{ verifySuccess ? '✓' : '➤' }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getCaptcha, checkCaptcha } from '@/api/captcha'

export default {
  name: 'SlideCaptcha',

  emits: ['verified', 'error'],

  data() {
    return {
      // 验证码相关状态
      captchaToken: '',
      captchaVerification: '',
      verified: false,

      // 验证码数据
      captchaData: {
        token: '',
        originalImageBase64: '',
        jigsawImageBase64: '',
        secretKey: '',
        result: false
      },

      // 图片尺寸配置 (rpx单位)
      imgWidth: 620,  // 310px * 2 = 620rpx
      imgHeight: 310, // 155px * 2 = 310rpx
      blockWidth: 94, // 47px * 2 = 94rpx

      // 滑块状态
      sliderLeft: 0,
      blockLeft: 0,
      isMoving: false,
      startX: 0,
      verifySuccess: false,

      // 文本提示
      sliderText: '向右滑动完成验证',
      finishText: '验证成功',
      tipMessage: ''
    }
  },

  mounted() {
    this.initCaptcha()
  },

  methods: {
    // 初始化验证码
    async initCaptcha() {
      try {
        const response = await getCaptcha()
        if (response.success && response.data) {
          this.captchaData = response.data
          this.resetSlider()
        } else {
          this.showTip('验证码加载失败', false)
        }
      } catch (error) {
        console.error('获取验证码失败:', error)
        this.showTip('验证码加载失败', false)
        this.$emit('error', '验证码加载失败')
      }
    },

    // 刷新验证码
    async refreshCaptcha() {
      this.resetCaptcha()
      await this.initCaptcha()
    },

    // 重置验证码状态
    resetCaptcha() {
      this.captchaData = {
        token: '',
        originalImageBase64: '',
        jigsawImageBase64: '',
        secretKey: '',
        result: false
      }
      this.resetSlider()
      this.tipMessage = ''
      this.verifySuccess = false
      this.verified = false
    },

    // 重置滑块状态
    resetSlider() {
      this.sliderLeft = 0
      this.blockLeft = 0
      this.isMoving = false
      this.verifySuccess = false
    },

    // 触摸开始
    handleTouchStart(e) {
      if (this.verifySuccess) return

      this.isMoving = true

      // 获取触摸起始位置，兼容不同平台
      let startX = 0
      if (e.changedTouches && e.changedTouches[0]) {
        startX = e.changedTouches[0].clientX
      } else if (e.touches && e.touches[0]) {
        startX = e.touches[0].clientX
      } else if (e.detail) {
        startX = e.detail.x
      }

      this.startX = startX
      this.tipMessage = ''
    },

    // 触摸移动
    handleTouchMove(e) {
      if (!this.isMoving || this.verifySuccess) return

      // 获取当前触摸位置，兼容不同平台
      let currentX = 0
      if (e.changedTouches && e.changedTouches[0]) {
        currentX = e.changedTouches[0].clientX
      } else if (e.touches && e.touches[0]) {
        currentX = e.touches[0].clientX
      } else if (e.detail) {
        currentX = e.detail.x
      }

      const deltaX = currentX - this.startX

      // 计算滑块位置，限制在有效范围内
      const deltaXRpx = deltaX * 2  // 将px转换为rpx
      const maxBlockDistance = this.imgWidth - this.blockWidth
      const maxSliderDistance = this.imgWidth - 80

      const newBlockLeft = Math.max(0, Math.min(deltaXRpx, maxBlockDistance))
      const newSliderLeft = Math.max(0, Math.min(deltaXRpx, maxSliderDistance))

      this.blockLeft = newBlockLeft
      this.sliderLeft = newSliderLeft
    },

    // 触摸结束
    async handleTouchEnd(e) {
      if (!this.isMoving || this.verifySuccess) return

      this.isMoving = false
      await this.verifyCaptchaPosition()
    },

    // 显示提示信息
    showTip(message, isSuccess) {
      this.tipMessage = message
      this.verifySuccess = isSuccess

      setTimeout(() => {
        if (!isSuccess) {
          this.tipMessage = ''
        }
      }, 3000)
    },

    // 验证验证码位置
    async verifyCaptchaPosition() {
      try {
        const pixelX = Math.round(this.blockLeft / 2)

        const verifyData = {
          captchaType: 'blockPuzzle',
          token: this.captchaData.token,
          pointJson: JSON.stringify({
            x: pixelX,
            y: 5
          }),
          verification: this.captchaData.secretKey
        }

        const response = await checkCaptcha(verifyData)

        if (response.success && response.data && response.data.result) {
          this.verifySuccess = true
          this.verified = true
          this.captchaToken = this.captchaData.token
          this.captchaVerification = this.captchaData.secretKey
          this.showTip('验证成功', true)

          // 发送验证成功事件
          this.$emit('verified', {
            token: this.captchaToken,
            verification: this.captchaVerification
          })
        } else {
          this.showTip('验证失败，请重试', false)
          this.resetSlider()
        }
      } catch (error) {
        console.error('验证失败:', error)
        this.showTip('验证异常，请重试', false)
        this.resetSlider()
        this.$emit('error', '验证异常')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.slide-captcha {
  width: 100%;
  margin-bottom: 30rpx;
}

.captcha-status {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 20rpx;
  margin-bottom: 20rpx;
  border-radius: 8rpx;
  background: #f0f9ff;
  border: 1px solid #0ea5e9;
}

.captcha-status.warning {
  background: #fef3c7;
  border-color: #f59e0b;
}

.status-icon {
  font-size: 24rpx;
  margin-right: 12rpx;
}

.status-text {
  font-size: 24rpx;
  color: #0ea5e9;
}

.captcha-status.warning .status-text {
  color: #f59e0b;
}

.inline-captcha {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 20rpx;
}

.captcha-image-panel {
  position: relative;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
  border: 2rpx solid #e2e8f0;
  background: #ffffff;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.captcha-bg-image {
  display: block;
  width: 100%;
  height: 100%;
}

.captcha-block {
  position: absolute;
  top: 0;
  z-index: 2;
  filter: drop-shadow(0 4rpx 12rpx rgba(0, 0, 0, 0.2));
}

.captcha-block-image {
  display: block;
  width: 100%;
  height: 100%;
  filter:
    hue-rotate(210deg)
    saturate(0.3)
    brightness(0.9)
    contrast(1.1)
    drop-shadow(2rpx 2rpx 4rpx rgba(0, 0, 0, 0.3))
    drop-shadow(-1rpx -1rpx 2rpx rgba(255, 255, 255, 0.8));
}

.captcha-refresh {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  z-index: 3;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.refresh-icon {
  font-size: 24rpx;
}

.captcha-tip {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 8rpx 16rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
  color: #ffffff;
  z-index: 3;
  white-space: nowrap;
}

.tip-success {
  background: #4f46e5;
}

.tip-error {
  background: #ff4d4f;
}

.captcha-slider {
  position: relative;
  background: #f7f8fa;
  border-radius: 40rpx;
  overflow: hidden;
  margin-top: 10rpx;
  box-shadow: inset 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

.slider-track {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 40rpx;
  overflow: hidden;
}

.slider-track-bg {
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, #f5f7fa, #e9ecef);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid #e2e8f0;
  border-radius: 40rpx;
}

.slider-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(90deg, #4f46e5, #6366f1);
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.slider-text {
  font-size: 28rpx;
  color: #999999;
  user-select: none;
  pointer-events: none;
}

.slider-fill .slider-text {
  color: #ffffff;
}

.slider-button {
  position: absolute;
  top: 0;
  left: 0;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border: 2rpx solid #e2e8f0;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.12), 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  z-index: 2;
  user-select: none;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.slider-button:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.15);
}

.slider-icon {
  font-size: 32rpx;
  transition: all 0.3s ease;
}

.icon-normal {
  color: #999999;
}

.icon-success {
  color: #4f46e5;
}
</style>