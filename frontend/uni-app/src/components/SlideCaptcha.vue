<template>
  <div 
    class="slide-captcha" 
    data-env="uniapp"
  >
    <SharedSlideCaptcha 
      :is-admin="false"
      :get-captcha="getCaptcha"
      :check-captcha="checkCaptcha"
      @verified="handleVerified"
      @error="handleError"
      ref="sharedCaptchaRef"
    />
  </div>
</template>

<script>
import { getCaptcha, checkCaptcha } from '@/api/captcha'
import SharedSlideCaptcha from '../../../shared/SlideCaptcha.vue'

export default {
  name: 'SlideCaptcha',
  
  components: {
    SharedSlideCaptcha
  },
  
  emits: ['verified', 'error'],
  
  methods: {
    getCaptcha,
    checkCaptcha,
    
    // 处理验证成功
    handleVerified(data) {
      this.$emit('verified', data)
    },

    // 处理错误
    handleError(message) {
      this.$emit('error', message)
    },

    // 重置验证码 (外部调用)
    reset() {
      this.$refs.sharedCaptchaRef?.reset()
    },

    // 获取验证状态
    get verified() {
      return this.$refs.sharedCaptchaRef?.verified
    },

    get captchaToken() {
      return this.$refs.sharedCaptchaRef?.captchaToken
    },

    get captchaVerification() {
      return this.$refs.sharedCaptchaRef?.captchaVerification
    }
  }
}
</script>