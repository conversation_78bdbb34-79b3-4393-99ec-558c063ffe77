<template>
  <view class="login-container">
    <!-- 头部logo区域 -->
    <view class="login-header">
      <view class="logo">
        <text class="logo-icon">🏥</text>
        <text class="logo-text">智能评估平台</text>
      </view>
      <text class="subtitle">多租户登录系统</text>
    </view>

    <!-- 登录表单 -->
    <view class="login-form">
      <!-- 租户代码输入框 -->
      <view class="form-item">
        <view class="form-label">
          <text class="label-text">机构代码</text>
          <text class="required">*</text>
        </view>
        <view class="input-wrapper">
          <text class="input-icon">🏢</text>
          <input
            class="input-field"
            type="text"
            v-model="loginForm.tenantCode"
            placeholder="请输入您的机构代码"
            maxlength="50"
            @input="clearError('tenantCode')"
          />
        </view>
        <text v-if="errors.tenantCode" class="error-text">{{ errors.tenantCode }}</text>
        <text class="hint-text">示例：demo_hospital</text>
      </view>

      <!-- 用户名输入框 -->
      <view class="form-item">
        <view class="form-label">
          <text class="label-text">用户名</text>
          <text class="required">*</text>
        </view>
        <view class="input-wrapper">
          <text class="input-icon">👤</text>
          <input
            class="input-field"
            type="text"
            v-model="loginForm.username"
            placeholder="请输入用户名"
            maxlength="50"
            @input="clearError('username')"
          />
        </view>
        <text v-if="errors.username" class="error-text">{{ errors.username }}</text>
      </view>

      <!-- 密码输入框 -->
      <view class="form-item">
        <view class="form-label">
          <text class="label-text">密码</text>
          <text class="required">*</text>
        </view>
        <view class="input-wrapper">
          <text class="input-icon">🔒</text>
          <input
            class="input-field"
            :type="showPassword ? 'text' : 'password'"
            v-model="loginForm.password"
            placeholder="请输入密码"
            maxlength="50"
            @input="clearError('password')"
          />
          <view class="password-toggle" @click="togglePassword">
            <text>{{ showPassword ? '👁️' : '👁️‍🗨️' }}</text>
          </view>
        </view>
        <text v-if="errors.password" class="error-text">{{ errors.password }}</text>
      </view>

      <!-- 记住登录选项 -->
      <view class="remember-section">
        <view class="checkbox-wrapper" @click="toggleRemember">
          <view class="checkbox" :class="{ checked: loginForm.rememberMe }">
            <text v-if="loginForm.rememberMe" class="check-icon">✓</text>
          </view>
          <text class="checkbox-label">记住登录状态</text>
        </view>
      </view>

      <!-- 内联滑动验证码区域 -->
      <view class="inline-captcha-section">
        <!-- 验证码状态提示 -->
        <view class="captcha-status" v-if="captchaVerified">
          <text class="status-icon">✅</text>
          <text class="status-text">滑动验证码已通过</text>
        </view>
        <view class="captcha-status warning" v-else>
          <text class="status-icon">🔒</text>
          <text class="status-text">请完成下方滑动验证码</text>
        </view>

        <!-- 内联滑动验证码 -->
        <view class="inline-captcha" v-if="!captchaVerified && captchaData.originalImageBase64">
          <!-- 背景图片 -->
          <view class="captcha-image-panel" :style="{width: imgWidth + 'rpx', height: imgHeight + 'rpx'}">
            <image 
              :src="'data:image/png;base64,' + captchaData.originalImageBase64" 
              class="captcha-bg-image"
              :style="{width: '100%', height: '100%'}"
              mode="aspectFit"
            />
            
            <!-- 滑块图片 -->
            <view 
              class="captcha-block"
              v-if="captchaData.jigsawImageBase64"
              :style="{
                left: blockLeft + 'rpx',
                top: '0rpx',
                width: blockWidth + 'rpx',
                height: imgHeight + 'rpx'
              }"
            >
              <image 
                :src="'data:image/png;base64,' + captchaData.jigsawImageBase64"
                class="captcha-block-image"
                :style="{width: '100%', height: '100%'}"
                mode="aspectFill"
              />
            </view>

            <!-- 刷新按钮 -->
            <view class="captcha-refresh" @click="refreshCaptcha">
              <text class="refresh-icon">🔄</text>
            </view>

            <!-- 提示信息 -->
            <view v-if="tipMessage" class="captcha-tip" :class="verifySuccess ? 'tip-success' : 'tip-error'">
              <text>{{ tipMessage }}</text>
            </view>
          </view>

          <!-- 滑动条 -->
          <view class="captcha-slider" :style="{width: imgWidth + 'rpx'}">
            <!-- 滑动轨道 -->
            <view class="slider-track">
              <view class="slider-track-bg">
                <text class="slider-text">{{ sliderText }}</text>
              </view>
              
              <!-- 已滑动区域 -->
              <view 
                class="slider-fill" 
                :style="{
                  width: sliderLeft + 'rpx',
                  transition: isMoving ? 'none' : 'all 0.3s ease'
                }"
              >
                <text class="slider-text">{{ finishText }}</text>
              </view>
            </view>

            <!-- 滑块 -->
            <view 
              class="slider-button"
              :style="{
                left: sliderLeft + 'rpx',
                transition: isMoving ? 'none' : 'all 0.3s ease'
              }"
              @touchstart.stop="handleTouchStart"
              @touchmove.stop.prevent="handleTouchMove" 
              @touchend.stop="handleTouchEnd"
              @mousedown.stop="handleMouseDown"
              @mousemove.stop="handleMouseMove"
              @mouseup.stop="handleMouseUp"
            >
              <text class="slider-icon" :class="verifySuccess ? 'icon-success' : 'icon-normal'">
                {{ verifySuccess ? '✓' : '➤' }}
              </text>
            </view>
          </view>
        </view>
      </view>

      <!-- 登录按钮 -->
      <view class="login-button-section">
        <button
          class="login-button"
          :class="{ disabled: loading || !captchaVerified }"
          @click="handleLogin"
          :disabled="loading || !captchaVerified"
        >
          <text v-if="loading">登录中...</text>
          <text v-else-if="!captchaVerified">请先完成验证码</text>
          <text v-else>登录</text>
        </button>
      </view>
    </view>

    <!-- 演示账户信息 -->
    <view class="demo-section">
      <view class="demo-header" @click="toggleDemoInfo">
        <text class="demo-title">💡 演示账户</text>
        <text class="demo-toggle">{{ showDemo ? '收起' : '展开' }}</text>
      </view>
      
      <view v-if="showDemo" class="demo-content">
        <view class="demo-item" @click="fillDemoAccount('hospital')">
          <text class="demo-type">🏥 医院机构</text>
          <view class="demo-details">
            <text class="demo-text">机构代码: demo_hospital</text>
            <text class="demo-text">用户名: demo_hospital_admin</text>
            <text class="demo-text">密码: password123</text>
          </view>
        </view>
        
        <view class="demo-item" @click="fillDemoAccount('nursing')">
          <text class="demo-type">🏠 护理机构</text>
          <view class="demo-details">
            <text class="demo-text">机构代码: demo_nursing</text>
            <text class="demo-text">用户名: demo_nursing_admin</text>
            <text class="demo-text">密码: password123</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 版本信息 -->
    <view class="version-info">
      <text class="version-text">智能评估平台 v2.0.0</text>
      <text class="copyright">支持多租户架构</text>
    </view>

  </view>
</template>

<script>
import { login } from '@/api/auth'
import { getCaptcha, checkCaptcha } from '@/api/captcha'
import { mapActions } from 'vuex'

export default {
  name: 'LoginPage',
  
  data() {
    return {
      loading: false,
      showPassword: false,
      showDemo: false,
      
      loginForm: {
        tenantCode: 'demo_hospital',
        username: 'demo_hospital_admin',
        password: 'password123',
        rememberMe: false
      },
      
      errors: {},
      
      // 验证码相关
      showCaptcha: true,  // 默认显示验证码
      captchaToken: '',
      captchaVerification: '',
      captchaVerified: false,  // 验证码是否已通过
      
      // 验证码数据
      captchaData: {
        token: '',
        originalImageBase64: '',
        jigsawImageBase64: '',
        secretKey: '',
        result: false,
        y: 0  // 添加Y坐标
      },
      
      // 图片尺寸配置 (rpx单位)
      imgWidth: 620,  // 310px * 2 = 620rpx
      imgHeight: 310, // 155px * 2 = 310rpx
      blockWidth: 94, // 47px * 2 = 94rpx
      
      // 滑块轨道的宽度
      sliderTrackWidth: 620, // 与图片宽度相同
      
      // 滑块状态
      sliderLeft: 0,
      blockLeft: 0,   // 拼图块的初始位置为0，需要滑动到正确位置
      isMoving: false,
      startX: 0,
      verifySuccess: false,
      
      // 文本提示
      sliderText: '向右滑动完成验证',
      finishText: '验证成功',
      tipMessage: ''
    }
  },
  
  onLoad() {
    // 检查是否已经登录
    const token = uni.getStorageSync('token')
    if (token) {
      this.redirectToMain()
    }
    
    // 自动填充上次登录信息
    this.loadRememberedInfo()
    
    // 初始化验证码
    this.initCaptcha()
  },
  
  methods: {
    ...mapActions('user', ['setUserInfo', 'setToken']),
    
    // 表单验证
    validateForm() {
      this.errors = {}
      
      if (!this.loginForm.tenantCode.trim()) {
        this.errors.tenantCode = '请输入机构代码'
      }
      
      if (!this.loginForm.username.trim()) {
        this.errors.username = '请输入用户名'
      }
      
      if (!this.loginForm.password.trim()) {
        this.errors.password = '请输入密码'
      } else if (this.loginForm.password.length < 6) {
        this.errors.password = '密码长度不能少于6位'
      }
      
      return Object.keys(this.errors).length === 0
    },
    
    // 清除单个字段错误
    clearError(field) {
      if (this.errors[field]) {
        delete this.errors[field]
        this.$forceUpdate()
      }
    },
    
    // 切换密码显示
    togglePassword() {
      this.showPassword = !this.showPassword
    },
    
    // 切换记住登录
    toggleRemember() {
      this.loginForm.rememberMe = !this.loginForm.rememberMe
    },
    
    // 切换演示信息显示
    toggleDemoInfo() {
      this.showDemo = !this.showDemo
    },
    
    // 填充演示账户
    fillDemoAccount(type) {
      const accounts = {
        hospital: {
          tenantCode: 'demo_hospital',
          username: 'demo_hospital_admin',
          password: 'password123'
        },
        nursing: {
          tenantCode: 'demo_nursing',
          username: 'demo_nursing_admin', 
          password: 'password123'
        }
      }
      
      const account = accounts[type]
      if (account) {
        this.loginForm.tenantCode = account.tenantCode
        this.loginForm.username = account.username
        this.loginForm.password = account.password
        this.clearAllErrors()
        
        uni.showToast({
          title: '演示账户已填充',
          icon: 'success',
          duration: 1500
        })
      }
    },
    
    // 清除所有错误
    clearAllErrors() {
      this.errors = {}
    },
    
    // 加载记住的登录信息
    loadRememberedInfo() {
      try {
        const remembered = uni.getStorageSync('rememberedLogin')
        if (remembered) {
          const info = JSON.parse(remembered)
          this.loginForm.tenantCode = info.tenantCode || ''
          this.loginForm.username = info.username || ''
          this.loginForm.rememberMe = true
        }
      } catch (error) {
        console.error('加载记住的登录信息失败:', error)
      }
    },
    
    // 保存登录信息
    saveLoginInfo() {
      if (this.loginForm.rememberMe) {
        const info = {
          tenantCode: this.loginForm.tenantCode,
          username: this.loginForm.username
        }
        uni.setStorageSync('rememberedLogin', JSON.stringify(info))
      } else {
        uni.removeStorageSync('rememberedLogin')
      }
    },
    
    // 处理登录
    async handleLogin() {
      if (this.loading) return
      
      // 表单验证
      if (!this.validateForm()) {
        uni.showToast({
          title: '请检查输入信息',
          icon: 'error'
        })
        return
      }

      // 检查验证码是否已通过
      if (!this.captchaVerified) {
        uni.showToast({
          title: '请先完成滑动验证码',
          icon: 'error'
        })
        return
      }
      
      this.loading = true
      
      try {
        // 添加客户端信息和验证码信息
        const loginData = {
          ...this.loginForm,
          clientInfo: `uni-app/${uni.getSystemInfoSync().platform}`
        }

        // 添加验证码信息（必须有）
        loginData.captchaToken = this.captchaToken
        loginData.captchaVerification = this.captchaVerification
        
        const response = await login(loginData)
        
        if (response.success || response.code === 200) {
          const result = response.data || response
          
          // 登录成功，清除失败次数
          uni.removeStorageSync('loginAttempts')
          
          // 保存登录信息
          this.saveLoginInfo()
          
          // 保存用户信息和token
          uni.setStorageSync('token', result.token)
          uni.setStorageSync('refreshToken', result.refreshToken || '')
          uni.setStorageSync('userInfo', JSON.stringify(result.user || {}))
          
          // 更新Vuex状态
          await this.setToken(result.token)
          await this.setUserInfo(result.user || {})
          
          uni.showToast({
            title: '登录成功',
            icon: 'success'
          })
          
          // 延迟跳转，让用户看到成功提示
          setTimeout(() => {
            this.redirectToMain()
          }, 1500)
          
        } else {
          throw new Error(response.message || '登录失败')
        }
        
      } catch (error) {
        console.error('登录失败:', error)
        
        // 记录登录失败次数
        const currentAttempts = uni.getStorageSync('loginAttempts') || 0
        uni.setStorageSync('loginAttempts', currentAttempts + 1)
        
        let errorMessage = '登录失败'
        if (error.message) {
          errorMessage = error.message
        } else if (error.data && error.data.message) {
          errorMessage = error.data.message
        }
        
        // 如果是验证码相关错误，清除验证码状态
        if (errorMessage.includes('验证码')) {
          this.resetCaptcha()
        }
        
        uni.showToast({
          title: errorMessage,
          icon: 'error',
          duration: 3000
        })
      } finally {
        this.loading = false
      }
    },

    
    // 跳转到主页
    redirectToMain() {
      uni.reLaunch({
        url: '/pages/index/index'
      })
    },

    // 验证码相关方法
    // 初始化验证码
    async initCaptcha() {
      try {
        const response = await getCaptcha()
        if (response.success && response.data) {
          this.captchaData = response.data
          this.resetSlider()
        } else {
          this.showTip('验证码加载失败', false)
        }
      } catch (error) {
        console.error('获取验证码失败:', error)
        this.showTip('验证码加载失败', false)
      }
    },

    // 刷新验证码
    async refreshCaptcha() {
      this.resetCaptcha()
      await this.initCaptcha()
    },

    // 重置验证码状态
    resetCaptcha() {
      this.captchaData = {
        token: '',
        originalImageBase64: '',
        jigsawImageBase64: '',
        secretKey: '',
        result: false,
        y: 0
      }
      this.resetSlider()
      this.tipMessage = ''
      this.verifySuccess = false
      this.captchaVerified = false
    },

    // 重置滑块状态
    resetSlider() {
      this.sliderLeft = 0
      this.blockLeft = 0  // 拼图块回到初始位置
      this.isMoving = false
      this.verifySuccess = false
    },

    // 触摸开始
    handleTouchStart(e) {
      if (this.verifySuccess) return
      
      this.isMoving = true
      
      // 获取触摸起始位置，兼容不同平台
      let startX = 0
      if (e.changedTouches && e.changedTouches[0]) {
        startX = e.changedTouches[0].clientX
      } else if (e.touches && e.touches[0]) {
        startX = e.touches[0].clientX
      } else if (e.detail) {
        startX = e.detail.x
      }
      
      this.startX = startX
      this.tipMessage = ''
      
      console.log('触摸开始:', this.startX)
    },

    // 触摸移动
    handleTouchMove(e) {
      if (!this.isMoving || this.verifySuccess) return

      // 获取当前触摸位置，兼容不同平台
      let currentX = 0
      if (e.changedTouches && e.changedTouches[0]) {
        currentX = e.changedTouches[0].clientX
      } else if (e.touches && e.touches[0]) {
        currentX = e.touches[0].clientX
      } else if (e.detail) {
        currentX = e.detail.x
      }
      
      const deltaX = currentX - this.startX
      
      console.log('触摸移动:', currentX, 'deltaX:', deltaX)
      
      // 计算滑块位置，限制在有效范围内
      // 对于拼图块：最大滑动距离 = 图片宽度 - 拼图块宽度
      // 对于滑动条：最大滑动距离 = 滑动条宽度 - 滑块按钮宽度
      const maxBlockDistance = this.imgWidth - this.blockWidth  // 拼图块的最大移动距离
      const maxSliderDistance = this.imgWidth - 80  // 滑动条滑块的最大移动距离(80rpx是滑块按钮宽度)
      
      // 计算实际移动距离，以较小者为准
      const newBlockLeft = Math.max(0, Math.min(deltaX, maxBlockDistance))
      const newSliderLeft = Math.max(0, Math.min(deltaX, maxSliderDistance))
      
      this.blockLeft = newBlockLeft
      this.sliderLeft = newSliderLeft
      
      console.log('滑块位置:', newSliderLeft)
    },

    // 触摸结束
    async handleTouchEnd(e) {
      if (!this.isMoving || this.verifySuccess) return
      
      this.isMoving = false
      console.log('触摸结束，最终位置:', this.sliderLeft)

      // 验证滑动距离
      await this.verifyCaptchaPosition()
    },

    // 显示提示信息
    showTip(message, isSuccess) {
      this.tipMessage = message
      this.verifySuccess = isSuccess
      
      // 3秒后清除提示
      setTimeout(() => {
        if (!isSuccess) {
          this.tipMessage = ''
        }
      }, 3000)
    },

    // 鼠标事件 (H5端fallback)
    handleMouseDown(e) {
      if (this.verifySuccess) return
      
      this.isMoving = true
      this.startX = e.clientX
      this.tipMessage = ''
      
      console.log('鼠标按下:', this.startX)
      
      // 添加全局鼠标事件监听
      document.addEventListener('mousemove', this.handleMouseMove)
      document.addEventListener('mouseup', this.handleMouseUp)
    },

    handleMouseMove(e) {
      if (!this.isMoving || this.verifySuccess) return

      const deltaX = e.clientX - this.startX
      
      console.log('鼠标移动:', e.clientX, 'deltaX:', deltaX)
      
      // 计算滑块位置，限制在有效范围内
      const deltaXRpx = deltaX * 2  // 将px转换为rpx
      const maxBlockDistance = this.imgWidth - this.blockWidth  // 拼图块的最大移动距离
      const maxSliderDistance = this.imgWidth - 80  // 滑动条滑块的最大移动距离
      
      // 计算实际移动距离
      const newBlockLeft = Math.max(0, Math.min(deltaXRpx, maxBlockDistance))
      const newSliderLeft = Math.max(0, Math.min(deltaXRpx, maxSliderDistance))
      
      this.blockLeft = newBlockLeft
      this.sliderLeft = newSliderLeft
      
      console.log('滑块位置:', newSliderLeft)
    },

    async handleMouseUp(e) {
      if (!this.isMoving || this.verifySuccess) return
      
      this.isMoving = false
      
      // 移除全局事件监听
      document.removeEventListener('mousemove', this.handleMouseMove)
      document.removeEventListener('mouseup', this.handleMouseUp)
      
      console.log('鼠标释放，最终位置:', this.sliderLeft)
      
      // 验证滑动距离
      await this.verifyCaptchaPosition()
    },

    // 验证验证码位置 (提取为公共方法)
    async verifyCaptchaPosition() {
      try {
        // 发送拼图块的位置坐标（将rpx转换为px）
        const pixelX = Math.round(this.blockLeft / 2)
        
        const verifyData = {
          captchaType: 'blockPuzzle',
          token: this.captchaData.token,
          pointJson: JSON.stringify({
            x: pixelX,
            y: 5
          }),
          verification: this.captchaData.secretKey
        }
        
        console.log('验证数据:', verifyData)

        const response = await checkCaptcha(verifyData)
        
        if (response.success && response.data && response.data.result) {
          this.verifySuccess = true
          this.captchaVerified = true
          this.captchaToken = this.captchaData.token
          this.captchaVerification = this.captchaData.secretKey
          this.showTip('验证成功', true)
        } else {
          this.showTip('验证失败，请重试', false)
          this.resetSlider()
        }
      } catch (error) {
        console.error('验证失败:', error)
        this.showTip('验证异常，请重试', false)
        this.resetSlider()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@use '@/common/styles/variables.scss' as *;

.login-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 40rpx 40rpx 40rpx;
  display: flex;
  flex-direction: column;
}

.login-header {
  text-align: center;
  margin-bottom: 80rpx;
  margin-top: 120rpx;
}

.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
}

.logo-icon {
  font-size: 64rpx;
  margin-right: 20rpx;
}

.logo-text {
  font-size: 48rpx;
  font-weight: 600;
  color: #333333;
}

.subtitle {
  font-size: 28rpx;
  color: #666666;
}

.login-form {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
}

.form-item {
  margin-bottom: 40rpx;
}

.form-label {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.label-text {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

.required {
  color: #ff4757;
  margin-left: 8rpx;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 24rpx 20rpx;
  transition: all 0.3s ease;
}

.input-wrapper:focus-within {
  border-color: #5357A0;
  background: #ffffff;
  box-shadow: 0 0 0 6rpx rgba(83, 87, 160, 0.1);
}

.input-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
  color: #6c757d;
}

.input-field {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
  background: transparent;
  border: none;
  outline: none;
}

.input-field::placeholder {
  color: #adb5bd;
}

.password-toggle {
  padding: 10rpx;
  cursor: pointer;
}

.error-text {
  font-size: 24rpx;
  color: #ff4757;
  margin-top: 12rpx;
  display: block;
}

.hint-text {
  font-size: 24rpx;
  color: #6c757d;
  margin-top: 8rpx;
  display: block;
}

.remember-section {
  margin-bottom: 60rpx;
}

.checkbox-wrapper {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.checkbox {
  width: 36rpx;
  height: 36rpx;
  border: 2rpx solid #dee2e6;
  border-radius: 6rpx;
  margin-right: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.checkbox.checked {
  background: #5357A0;
  border-color: #5357A0;
}

.check-icon {
  color: #ffffff;
  font-size: 20rpx;
  font-weight: bold;
}

.checkbox-label {
  font-size: 26rpx;
  color: #6c757d;
}

.captcha-status {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 20rpx;
  margin-bottom: 20rpx;
  border-radius: 8rpx;
  background: #f0f9ff;
  border: 1px solid #0ea5e9;
}

.captcha-status.warning {
  background: #fef3c7;
  border-color: #f59e0b;
}

.status-icon {
  font-size: 24rpx;
  margin-right: 12rpx;
}

.status-text {
  font-size: 24rpx;
  color: #0ea5e9;
}

.captcha-status.warning .status-text {
  color: #f59e0b;
}

/* 内联验证码样式 */
.inline-captcha-section {
  margin-bottom: 30rpx;
}

.inline-captcha {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 20rpx;
}

.captcha-image-panel {
  position: relative;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
  border: 2rpx solid #e2e8f0;
  background: #ffffff;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.captcha-bg-image {
  display: block;
  width: 100%;
  height: 100%;
}

.captcha-block {
  position: absolute;
  top: 0;
  z-index: 2;
  filter: drop-shadow(0 4rpx 12rpx rgba(0, 0, 0, 0.2));
}

.captcha-block-image {
  display: block;
  width: 100%;
  height: 100%;
  /* 使用CSS滤镜将白色拼图块变为浅色凹凸效果 */
  filter: 
    hue-rotate(210deg) 
    saturate(0.3) 
    brightness(0.9) 
    contrast(1.1)
    drop-shadow(2rpx 2rpx 4rpx rgba(0, 0, 0, 0.3))
    drop-shadow(-1rpx -1rpx 2rpx rgba(255, 255, 255, 0.8));
}

.captcha-refresh {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  z-index: 3;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.refresh-icon {
  font-size: 24rpx;
}

.captcha-tip {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 8rpx 16rpx;
  border-radius: 6rpx;
  font-size: 24rpx;
  color: #ffffff;
  z-index: 3;
  white-space: nowrap;
}

.tip-success {
  background: #4f46e5;
}

.tip-error {
  background: #ff4d4f;
}

.captcha-slider {
  width: 100%;
  height: 80rpx;
  position: relative;
  background: #f7f8fa;
  border-radius: 40rpx;
  overflow: hidden;
  margin-top: 10rpx;
  box-shadow: inset 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

.slider-track {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 40rpx;
  overflow: hidden;
}

.slider-track-bg {
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, #f5f7fa, #e9ecef);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid #e2e8f0;
  border-radius: 40rpx;
}

.slider-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(90deg, #4f46e5, #6366f1);
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.slider-text {
  font-size: 28rpx;
  color: #999999;
  user-select: none;
  pointer-events: none;
}

.slider-fill .slider-text {
  color: #ffffff;
}

.slider-button {
  position: absolute;
  top: 0;
  left: 0;
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border: 2rpx solid #e2e8f0;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.12), 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  z-index: 2;
  user-select: none;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.slider-button:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.15);
}

.slider-icon {
  font-size: 32rpx;
  transition: all 0.3s ease;
}

.icon-normal {
  color: #999999;
}

.icon-success {
  color: #4f46e5;
}

.login-button-section {
  margin-bottom: 20rpx;
}

.login-button {
  width: 100%;
  background: #5357A0;
  color: #ffffff;
  border: none;
  border-radius: 12rpx;
  padding: 28rpx;
  font-size: 32rpx;
  font-weight: 600;
  transition: all 0.3s ease;
}

.login-button:not(.disabled):active {
  transform: translateY(2rpx);
}

.login-button.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.demo-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  margin-bottom: 40rpx;
  overflow: hidden;
}

.demo-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 32rpx;
  cursor: pointer;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.demo-title {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

.demo-toggle {
  font-size: 24rpx;
  color: #5357A0;
}

.demo-content {
  padding: 20rpx 32rpx 32rpx;
}

.demo-item {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}

.demo-item:last-child {
  margin-bottom: 0;
}

.demo-item:active {
  background: #e9ecef;
  transform: scale(0.98);
}

.demo-type {
  font-size: 26rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 12rpx;
  display: block;
}

.demo-details {
  display: flex;
  flex-direction: column;
}

.demo-text {
  font-size: 22rpx;
  color: #6c757d;
  margin-bottom: 4rpx;
}

.demo-text:last-child {
  margin-bottom: 0;
}

.version-info {
  text-align: center;
  margin-top: auto;
  padding-top: 40rpx;
}

.version-text {
  font-size: 24rpx;
  color: #999999;
  margin-bottom: 8rpx;
  display: block;
}

.copyright {
  font-size: 22rpx;
  color: #aaaaaa;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .login-container {
    padding: 30rpx 30rpx;
  }
  
  .login-header {
    margin-top: 80rpx;
    margin-bottom: 60rpx;
  }
  
  .logo-text {
    font-size: 40rpx;
  }
  
  .login-form {
    padding: 40rpx 30rpx;
  }
}
</style>