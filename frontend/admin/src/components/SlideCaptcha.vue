<template>
  <div class="slide-captcha" data-env="admin">
    <SharedSlideCaptcha 
      :is-admin="true"
      :get-captcha="getCaptcha"
      :check-captcha="checkCaptcha"
      @verified="handleVerified"
      @error="handleError"
      ref="sharedCaptchaRef"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, defineEmits, defineExpose, onMounted } from 'vue';
import { getCaptcha, checkCaptcha } from '@/api/captcha';
import SharedSlideCaptcha from '../../../shared/SlideCaptcha.vue';

onMounted(() => {
  console.log('📱 Admin SlideCaptcha 包装组件已挂载')
});

// 定义事件
const emit = defineEmits<{
  verified: [data: { token: string; verification: string }]
  error: [message: string]
}>();

const sharedCaptchaRef = ref();

// 处理验证成功
const handleVerified = (data: { token: string; verification: string }) => {
  emit('verified', data);
};

// 处理错误
const handleError = (message: string) => {
  emit('error', message);
};

// 暴露给父组件的方法和状态
defineExpose({
  reset() {
    sharedCaptchaRef.value?.reset();
  },
  get verified() {
    return sharedCaptchaRef.value?.verified;
  },
  get captchaToken() {
    return sharedCaptchaRef.value?.captchaToken;
  },
  get captchaVerification() {
    return sharedCaptchaRef.value?.captchaVerification;
  }
});
</script>