/**
 * 滑动验证码共享配置
 * 两个前端项目共用此配置文件
 */

export const CAPTCHA_CONFIG = {
  // 图片尺寸配置
  IMAGE_WIDTH: 310,
  IMAGE_HEIGHT: 155,
  BLOCK_WIDTH: 47,
  
  // 滑块配置
  SLIDER_HEIGHT: 40,
  SLIDER_BUTTON_SIZE: 40,
  
  // 文本配置
  TEXTS: {
    SLIDE_TEXT: '向右滑动完成验证',
    SUCCESS_TEXT: '验证成功',
    LOADING_TEXT: '验证中...',
    ERROR_TEXT: '验证失败，请重试',
    NETWORK_ERROR: '网络异常，请重试',
    VERIFIED_STATUS: '滑动验证码已通过',
    UNVERIFIED_STATUS: '请完成下方滑动验证码'
  },
  
  // API配置
  API: {
    GET_CAPTCHA: '/api/captcha/get',
    CHECK_CAPTCHA: '/api/captcha/check',
    CAPTCHA_TYPE: 'blockPuzzle'
  },
  
  // 验证配置
  VERIFICATION: {
    MAX_SLIDE_DISTANCE_RATIO: 0.87, // 最大滑动距离比例 (310-40)/310
    Y_COORDINATE: 5, // 固定Y坐标
    TIP_DISPLAY_DURATION: 3000 // 提示显示时长
  },
  
  // 颜色配置（可以被各端覆盖）
  COLORS: {
    SUCCESS: '#4f46e5',
    ERROR: '#ff4d4f',
    WARNING: '#f59e0b',
    PRIMARY: '#0ea5e9',
    BORDER: '#e2e8f0',
    TEXT_NORMAL: '#999999',
    TEXT_WHITE: '#ffffff',
    
    // 状态背景色
    STATUS_SUCCESS_BG: '#f0f9ff',
    STATUS_WARNING_BG: '#fef3c7',
    
    // 滑动条配色
    SLIDER_TRACK_BG: '#f7f8fa',
    SLIDER_TRACK_GRADIENT: 'linear-gradient(to right, #f5f7fa, #e9ecef)',
    SLIDER_FILL_GRADIENT: 'linear-gradient(90deg, #4f46e5, #6366f1)',
    SLIDER_BUTTON_BG: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',
    
    // 图片面板
    IMAGE_PANEL_BG: '#ffffff',
    IMAGE_PANEL_BORDER: '#e2e8f0',
    
    // 刷新按钮
    REFRESH_BUTTON_BG: 'rgba(255, 255, 255, 0.9)'
  },
  
  // 样式配置
  STYLES: {
    // 边框圆角
    BORDER_RADIUS: {
      SMALL: 6,
      MEDIUM: 12,
      LARGE: 40
    },
    
    // 阴影
    SHADOWS: {
      LIGHT: '0 2px 8px rgba(0, 0, 0, 0.06)',
      MEDIUM: '0 4px 16px rgba(0, 0, 0, 0.06)',
      BUTTON: '0 4px 20px rgba(0, 0, 0, 0.12), 0 2px 8px rgba(0, 0, 0, 0.08)',
      SLIDER_INSET: 'inset 0 2px 8px rgba(0, 0, 0, 0.08)',
      BLOCK_SOFT: '0 2px 12px rgba(79, 70, 229, 0.3)'
    },
    
    // 过渡动画
    TRANSITIONS: {
      FAST: '0.2s ease',
      NORMAL: '0.3s ease'
    },
    
    // 滑块优化样式
    BLOCK_STYLE: {
      // 移除黑色边框，使用柔和阴影
      BORDER: 'none',
      BORDER_RADIUS: '8px',
      SHADOW: '0 2px 12px rgba(79, 70, 229, 0.3)',
      // 滑块图片滤镜优化
      FILTER: 'drop-shadow(0 2px 8px rgba(79, 70, 229, 0.25))',
      // 背景高亮效果
      BACKGROUND_HIGHLIGHT: 'rgba(79, 70, 229, 0.1)'
    }
  }
};

/**
 * 计算最大滑动距离
 * @param {number} containerWidth 容器宽度
 * @param {number} sliderWidth 滑块宽度  
 * @returns {number} 最大滑动距离
 */
export function getMaxSlideDistance(containerWidth, sliderWidth) {
  return containerWidth - sliderWidth;
}

/**
 * 格式化验证数据
 * @param {string} token 验证码token
 * @param {number} x X坐标
 * @param {string} secretKey 密钥
 * @returns {object} 格式化的验证数据
 */
export function formatVerifyData(token, x, secretKey) {
  return {
    captchaType: CAPTCHA_CONFIG.API.CAPTCHA_TYPE,
    token,
    pointJson: JSON.stringify({
      x: Math.round(x),
      y: CAPTCHA_CONFIG.VERIFICATION.Y_COORDINATE
    }),
    verification: secretKey
  };
}

/**
 * 单位转换工具
 */
export const UNITS = {
  // px转rpx (uni-app用)
  pxToRpx: (px) => px * 2,
  // rpx转px
  rpxToPx: (rpx) => rpx / 2
};