<template>
  <div class="slide-captcha" :data-env="isUniApp ? 'uniapp' : 'admin'">
    <!-- 验证码状态提示 -->
    <div class="captcha-status" v-if="verified">
      <span class="status-icon">✅</span>
      <span class="status-text">{{ config?.TEXTS?.VERIFIED_STATUS || '滑动验证码已通过' }}</span>
    </div>
    <div class="captcha-status warning" v-else>
      <span class="status-icon">🔒</span>
      <span class="status-text">{{ config?.TEXTS?.UNVERIFIED_STATUS || '请完成下方滑动验证码' }}</span>
    </div>

    <!-- 内嵌滑动验证码 -->
    <div v-if="!verified && captchaData?.originalImageBase64" class="inline-captcha">
      <!-- 背景图片容器 -->
      <div
        class="captcha-image-panel"
        :style="getImagePanelStyle()"
      >
        <!-- 背景图片 -->
        <img
          :src="'data:image/png;base64,' + (captchaData?.originalImageBase64 || '')"
          class="captcha-bg-image"
          draggable="false"
        />

        <!-- 目标区域提示 (仅在未验证时显示) -->
        <div
          v-if="captchaData?.jigsawImageBase64 && !verifySuccess"
          class="captcha-target-hint"
          :style="getTargetHintStyle()"
        />

        <!-- 滑块图片 -->
        <div
          v-if="captchaData?.jigsawImageBase64"
          class="captcha-block"
          :style="getBlockStyle()"
        >
          <img
            :src="'data:image/png;base64,' + (captchaData?.jigsawImageBase64 || '')"
            class="captcha-block-image"
            draggable="false"
          />
        </div>

        <!-- 刷新按钮 -->
        <div
          class="captcha-refresh"
          @click="refreshCaptcha"
        >
          <span class="refresh-icon">🔄</span>
        </div>

        <!-- 提示信息 -->
        <transition name="tip-fade">
          <div v-if="tipMessage" class="captcha-tip" :class="verifySuccess ? 'tip-success' : 'tip-error'">
            {{ tipMessage }}
          </div>
        </transition>
      </div>

      <!-- 滑动条 -->
      <div class="captcha-slider" :style="getSliderStyle()">
        <!-- 滑动轨道 -->
        <div class="slider-track">
          <div class="slider-track-bg">
            <span class="slider-text">{{ sliderText }}</span>
          </div>

          <!-- 已滑动区域 -->
          <div
            class="slider-fill"
            :style="getSliderFillStyle()"
          >
            <span class="slider-text">{{ finishText }}</span>
          </div>
        </div>

        <!-- 滑块 -->
        <div
          class="slider-button"
          :style="getSliderButtonStyle()"
          @mousedown="handleMouseDown"
          @touchstart="handleTouchStart"
        >
          <span class="slider-icon" :class="verifySuccess ? 'icon-success' : 'icon-normal'">
            {{ verifySuccess ? '✓' : '➤' }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, getCurrentInstance } from 'vue'
import { CAPTCHA_CONFIG, formatVerifyData, UNITS } from './captcha-config.js'

// 环境检测工具
const detectEnvironment = () => {
  // 检测是否为uni-app环境
  const isUniApp = typeof uni !== 'undefined'
  // 检测Vue版本 (Vue 3有getCurrentInstance)
  const isVue3 = typeof getCurrentInstance !== 'undefined'

  return {
    isUniApp,
    isVue3,
    platform: isUniApp ? 'uni-app' : 'admin'
  }
}

export default {
  name: 'SlideCaptcha',

  props: {
    // API接口函数
    getCaptcha: {
      type: Function,
      required: true
    },
    checkCaptcha: {
      type: Function,
      required: true
    }
  },

  emits: ['verified', 'error'],

  setup(props, { emit }) {
    // 检测当前环境
    const env = detectEnvironment()

    // 如果是Vue 3环境，使用Composition API
    if (env.isVue3) {
      // 环境信息
      const isUniApp = ref(env.isUniApp)
      const platform = ref(env.platform)
      const config = ref(CAPTCHA_CONFIG)

      // 验证码相关状态
      const captchaToken = ref('')
      const captchaVerification = ref('')
      const verified = ref(false)

      // 验证码数据
      const captchaData = reactive({
        token: '',
        originalImageBase64: '',
        jigsawImageBase64: '',
        secretKey: '',
        result: false
      })

      // 计算属性：是否为admin环境
      const isAdmin = computed(() => !isUniApp.value)

      // 图片尺寸配置（根据环境自动调整）
      const imgWidth = computed(() =>
        isAdmin.value ? CAPTCHA_CONFIG.IMAGE_WIDTH : UNITS.pxToRpx(CAPTCHA_CONFIG.IMAGE_WIDTH)
      )
      const imgHeight = computed(() =>
        isAdmin.value ? CAPTCHA_CONFIG.IMAGE_HEIGHT : UNITS.pxToRpx(CAPTCHA_CONFIG.IMAGE_HEIGHT)
      )
      const blockWidth = computed(() =>
        isAdmin.value ? CAPTCHA_CONFIG.BLOCK_WIDTH : UNITS.pxToRpx(CAPTCHA_CONFIG.BLOCK_WIDTH)
      )

      // 滑块状态
      const sliderLeft = ref(0)
      const blockLeft = ref(0)
      const isMoving = ref(false)
      const startX = ref(0)
      const verifySuccess = ref(false)

      // 文本提示
      const sliderText = ref(CAPTCHA_CONFIG.TEXTS.SLIDE_TEXT)
      const finishText = ref(CAPTCHA_CONFIG.TEXTS.SUCCESS_TEXT)
      const tipMessage = ref('')

      // 返回Vue 3的响应式数据和方法
      return {
        // 环境信息
        isUniApp,
        platform,
        config,
        isAdmin,

        // 验证码相关状态
        captchaToken,
        captchaVerification,
        verified,
        captchaData,

        // 图片尺寸
        imgWidth,
        imgHeight,
        blockWidth,

        // 滑块状态
        sliderLeft,
        blockLeft,
        isMoving,
        startX,
        verifySuccess,

        // 文本提示
        sliderText,
        finishText,
        tipMessage
      }
    }

    // Vue 2兼容性返回null，使用data()和methods
    return null
  },

  data() {
    // Vue 2环境的数据
    const env = detectEnvironment()
    const isAdmin = !env.isUniApp

    return {
      // 环境信息
      isUniApp: env.isUniApp,
      isVue3: env.isVue3,
      platform: env.platform,

      // 共享配置
      config: CAPTCHA_CONFIG,

      // 验证码相关状态
      captchaToken: '',
      captchaVerification: '',
      verified: false,

      // 验证码数据
      captchaData: {
        token: '',
        originalImageBase64: '',
        jigsawImageBase64: '',
        secretKey: '',
        result: false
      },

      // 图片尺寸配置（根据环境自动调整）
      imgWidth: isAdmin ? CAPTCHA_CONFIG.IMAGE_WIDTH : UNITS.pxToRpx(CAPTCHA_CONFIG.IMAGE_WIDTH),
      imgHeight: isAdmin ? CAPTCHA_CONFIG.IMAGE_HEIGHT : UNITS.pxToRpx(CAPTCHA_CONFIG.IMAGE_HEIGHT),
      blockWidth: isAdmin ? CAPTCHA_CONFIG.BLOCK_WIDTH : UNITS.pxToRpx(CAPTCHA_CONFIG.BLOCK_WIDTH),

      // 滑块状态
      sliderLeft: 0,
      blockLeft: 0,
      isMoving: false,
      startX: 0,
      verifySuccess: false,

      // 文本提示
      sliderText: CAPTCHA_CONFIG.TEXTS.SLIDE_TEXT,
      finishText: CAPTCHA_CONFIG.TEXTS.SUCCESS_TEXT,
      tipMessage: ''
    }
  },

  computed: {
    // 计算属性：是否为admin环境
    isAdmin() {
      return !this.isUniApp
    }
  },

  mounted() {
    // 组件挂载后立即初始化验证码
    this.initCaptcha()
  },

  methods: {
    // 获取样式的通用方法
    getUnit(value) {
      return this.isAdmin ? value + 'px' : value + 'rpx'
    },

    getImagePanelStyle() {
      return {
        width: this.getUnit(this.imgWidth),
        height: this.getUnit(this.imgHeight)
      }
    },

    getTargetHintStyle() {
      return {
        left: this.getUnit(Math.round((this.imgWidth - this.blockWidth) * 0.75)),
        top: this.getUnit(this.isAdmin ? 20 : 40),
        width: this.getUnit(this.blockWidth),
        height: this.getUnit(this.imgHeight - (this.isAdmin ? 40 : 80))
      }
    },

    getBlockStyle() {
      return {
        left: this.getUnit(this.blockLeft),
        top: this.getUnit(0),
        width: this.getUnit(this.blockWidth),
        height: this.getUnit(this.imgHeight)
      }
    },

    getSliderStyle() {
      return {
        width: this.getUnit(this.imgWidth),
        height: this.getUnit(this.isAdmin ? 40 : 80)
      }
    },

    getSliderFillStyle() {
      return {
        width: this.getUnit(this.sliderLeft),
        transition: this.isMoving ? 'none' : 'all 0.3s ease'
      }
    },

    getSliderButtonStyle() {
      const buttonSize = this.isAdmin ? 40 : 80
      return {
        left: this.getUnit(this.sliderLeft),
        width: this.getUnit(buttonSize),
        height: this.getUnit(buttonSize),
        transition: this.isMoving ? 'none' : 'all 0.3s ease'
      }
    },

    // 初始化验证码 - 集成真实API
    async initCaptcha() {
      console.log('🚀 开始初始化验证码...', { platform: this.platform })

      try {
        const response = await this.getCaptcha()

        // 处理不同环境的响应格式
        let captchaData = null
        if (this.isAdmin) {
          // Admin环境：response.data.data
          captchaData = response?.data?.data || response?.data
        } else {
          // Uni-app环境：response.data 或 response
          captchaData = response?.data || response
        }

        if (captchaData && captchaData.originalImageBase64) {
          console.log('✅ 验证码数据加载成功:', {
            token: captchaData.token,
            hasOriginalImage: !!captchaData.originalImageBase64,
            hasJigsawImage: !!captchaData.jigsawImageBase64
          })

          Object.assign(this.captchaData, captchaData)
          this.resetSlider()
        } else {
          throw new Error('验证码数据格式错误')
        }
      } catch (error) {
        console.error('❌ 获取验证码失败:', error)
        this.showTip(this.config?.TEXTS?.NETWORK_ERROR || '验证码加载失败', false)
        this.$emit('error', error.message || '验证码加载失败')
      }
    },

    // 刷新验证码
    async refreshCaptcha() {
      this.resetCaptchaState()
      await this.initCaptcha()
    },

    // 重置验证码状态
    resetCaptchaState() {
      Object.assign(this.captchaData, {
        token: '',
        originalImageBase64: '',
        jigsawImageBase64: '',
        secretKey: '',
        result: false
      })
      this.resetSlider()
      this.tipMessage = ''
      this.verifySuccess = false
      this.verified = false
    },

    // 重置滑块状态
    resetSlider() {
      this.sliderLeft = 0
      this.blockLeft = 0
      this.isMoving = false
      this.verifySuccess = false
    },

    // 鼠标按下 (仅适用于admin环境)
    handleMouseDown(e) {
      if (this.verifySuccess || !this.isAdmin) return

      this.isMoving = true
      this.startX = e.clientX
      this.tipMessage = ''

      console.log('🖱️ 鼠标按下:', { platform: this.platform, startX: this.startX })

      // 添加全局鼠标事件监听
      document.addEventListener('mousemove', this.handleMouseMove)
      document.addEventListener('mouseup', this.handleMouseUp)

      // 防止文本选择
      e.preventDefault()
    },

    // 鼠标移动
    handleMouseMove(e) {
      if (!this.isMoving || this.verifySuccess || !this.isAdmin) return

      const deltaX = e.clientX - this.startX
      this.updateSliderPosition(deltaX)
    },

    // 鼠标松开
    async handleMouseUp() {
      if (!this.isMoving || this.verifySuccess || !this.isAdmin) return

      this.isMoving = false
      console.log('🖱️ 鼠标松开:', { platform: this.platform })

      // 移除全局事件监听
      document.removeEventListener('mousemove', this.handleMouseMove)
      document.removeEventListener('mouseup', this.handleMouseUp)

      // 验证滑动距离
      await this.verifyCaptchaPosition()
    },

    // 触摸开始 (兼容admin和uni-app环境)
    handleTouchStart(e) {
      if (this.verifySuccess) return

      this.isMoving = true
      this.tipMessage = ''

      // 获取触摸起始位置，兼容不同平台
      let startX = 0
      if (this.isAdmin) {
        // Admin环境：标准Web触摸事件
        startX = e.touches[0].clientX
        // 添加全局触摸事件监听
        document.addEventListener('touchmove', this.handleTouchMove, { passive: false })
        document.addEventListener('touchend', this.handleTouchEnd)
        e.preventDefault()
      } else {
        // Uni-app环境：处理uni-app特殊的触摸事件格式
        if (e.changedTouches && e.changedTouches[0]) {
          startX = e.changedTouches[0].clientX
        } else if (e.touches && e.touches[0]) {
          startX = e.touches[0].clientX
        } else if (e.detail && e.detail.x !== undefined) {
          startX = e.detail.x
        } else {
          // 备用方案：尝试从事件对象直接获取
          startX = e.clientX || 0
        }
      }

      this.startX = startX
      console.log('👆 触摸开始:', { platform: this.platform, startX })
    },

    // 触摸移动
    handleTouchMove(e) {
      if (!this.isMoving || this.verifySuccess) return

      let currentX = 0
      if (this.isAdmin) {
        // Admin环境：标准Web触摸事件
        currentX = e.touches[0].clientX
        e.preventDefault()
      } else {
        // Uni-app环境：处理uni-app特殊的触摸事件格式
        if (e.changedTouches && e.changedTouches[0]) {
          currentX = e.changedTouches[0].clientX
        } else if (e.touches && e.touches[0]) {
          currentX = e.touches[0].clientX
        } else if (e.detail && e.detail.x !== undefined) {
          currentX = e.detail.x
        } else {
          // 备用方案：尝试从事件对象直接获取
          currentX = e.clientX || this.startX
        }
      }

      const deltaX = currentX - this.startX
      this.updateSliderPosition(deltaX)
    },

    // 触摸结束
    async handleTouchEnd() {
      if (!this.isMoving || this.verifySuccess) return

      this.isMoving = false
      console.log('👆 触摸结束:', { platform: this.platform })

      if (this.isAdmin) {
        // 移除全局事件监听
        document.removeEventListener('touchmove', this.handleTouchMove)
        document.removeEventListener('touchend', this.handleTouchEnd)
      }

      // 验证滑动距离
      await this.verifyCaptchaPosition()
    },

    // 更新滑块位置
    updateSliderPosition(deltaX) {
      // 根据环境调整deltaX
      // Admin环境：直接使用px值
      // Uni-app环境：需要将px转换为rpx (1px = 2rpx)
      const adjustedDelta = this.isAdmin ? deltaX : deltaX * 2

      // 计算滑块位置，限制在有效范围内
      const buttonSize = this.isAdmin ?
        (this.config?.SLIDER_BUTTON_SIZE || 40) :
        UNITS.pxToRpx(this.config?.SLIDER_BUTTON_SIZE || 40)
      const maxSlideDistance = this.imgWidth - buttonSize
      const newSliderLeft = Math.max(0, Math.min(adjustedDelta, maxSlideDistance))

      this.sliderLeft = newSliderLeft
      this.blockLeft = newSliderLeft
    },

    // 验证验证码位置
    async verifyCaptchaPosition() {
      try {
        // 将滑动距离转换为像素坐标
        const pixelX = this.isAdmin ? Math.round(this.sliderLeft) : Math.round(this.sliderLeft / 2)

        const verifyData = formatVerifyData(
          this.captchaData.token,
          pixelX,
          this.captchaData.secretKey
        )

        console.log('🔍 验证数据:', {
          platform: this.platform,
          pixelX,
          verifyData
        })

        const response = await this.checkCaptcha(verifyData)

        // 处理不同环境的响应格式
        let responseData = null
        if (this.isAdmin) {
          // Admin环境：response.data.data 或 response.data
          responseData = response?.data?.data || response?.data || response
        } else {
          // Uni-app环境：response.data 或 response
          responseData = response?.data || response
        }

        console.log('📋 验证响应:', {
          platform: this.platform,
          responseData,
          success: responseData?.success,
          result: responseData?.data?.result || responseData?.result
        })

        // 检查验证结果
        const isSuccess = responseData?.success && (responseData?.data?.result || responseData?.result)

        if (isSuccess) {
          this.verifySuccess = true
          this.verified = true
          this.captchaToken = this.captchaData.token
          this.captchaVerification = this.captchaData.secretKey
          this.showTip(this.config?.TEXTS?.SUCCESS_TEXT || '验证成功', true)

          console.log('✅ 验证成功:', {
            token: this.captchaToken,
            verification: this.captchaVerification
          })

          // 触发验证成功事件
          this.$emit('verified', {
            token: this.captchaToken,
            verification: this.captchaVerification
          })
        } else {
          console.log('❌ 验证失败')
          this.showTip(this.config?.TEXTS?.ERROR_TEXT || '验证失败，请重试', false)
          this.resetSlider()
        }
      } catch (error) {
        console.error('❌ 验证过程出错:', error)
        this.showTip(this.config?.TEXTS?.ERROR_TEXT || '验证失败，请重试', false)
        this.resetSlider()
        this.$emit('error', error.message || '验证失败，请重试')
      }
    },

    // 显示提示信息
    showTip(message, isSuccess) {
      this.tipMessage = message
      this.verifySuccess = isSuccess

      console.log('💬 显示提示:', { message, isSuccess, platform: this.platform })

      // 根据配置时间清除提示
      setTimeout(() => {
        if (!isSuccess) {
          this.tipMessage = ''
        }
      }, this.config?.VERIFICATION?.TIP_DISPLAY_DURATION || 3000)
    },

    // 重置验证码 (外部调用)
    reset() {
      console.log('🔄 重置验证码:', { platform: this.platform })
      this.resetCaptchaState()
      this.initCaptcha()
    },

    // 获取验证状态 (外部调用)
    getVerificationData() {
      return {
        verified: this.verified,
        token: this.captchaToken,
        verification: this.captchaVerification
      }
    }
  }
}
</script>

<style scoped>
/* 验证码状态样式 */
.captcha-status {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 16px;
  margin-bottom: 16px;
  border-radius: 6px;
  background: #f0f9ff;
  border: 1px solid #0ea5e9;
  font-size: 14px;
}

.captcha-status.warning {
  background: #fef3c7;
  border-color: #f59e0b;
}

.status-icon {
  margin-right: 8px;
}

.status-text {
  color: #0ea5e9;
}

.captcha-status.warning .status-text {
  color: #f59e0b;
}

/* 统一现代化验证码样式 */
.inline-captcha {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 20px;
}

.captcha-image-panel {
  position: relative;
  margin-bottom: 20px;
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid #e2e8f0;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  box-shadow: 
    0 4px 16px rgba(0, 0, 0, 0.06),
    0 1px 4px rgba(0, 0, 0, 0.04);
}

.captcha-bg-image {
  display: block;
  width: 100%;
  height: 100%;
}

.captcha-target-hint {
  position: absolute;
  z-index: 1;
  border: 2px dashed rgba(79, 70, 229, 0.3);
  border-radius: 8px;
  background: rgba(79, 70, 229, 0.08);
  pointer-events: none;
  animation: targetPulse 2s infinite ease-in-out;
}

@keyframes targetPulse {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
}

.captcha-block {
  position: absolute;
  top: 0;
  z-index: 2;
  border: none;
  border-radius: 8px;
  overflow: hidden;
  filter: drop-shadow(0 2px 8px rgba(79, 70, 229, 0.25));
  background: rgba(79, 70, 229, 0.05);
}

.captcha-block-image {
  display: block;
  width: 100%;
  height: 100%;
  border-radius: 8px;
  filter: 
    brightness(1.05) 
    contrast(1.08)
    saturate(1.1)
    drop-shadow(0 1px 3px rgba(79, 70, 229, 0.2));
}

.captcha-refresh {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 3;
  background: rgba(255, 255, 255, 0.9) !important;
  border-radius: 50% !important;
  width: 30px !important;
  height: 30px !important;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 12px;
}

.refresh-icon {
  font-size: 14px;
}

.captcha-tip {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 12px;
  color: #ffffff;
  z-index: 3;
  white-space: nowrap;
  font-weight: bold;
}

.tip-success {
  background: #4f46e5;
}

.tip-error {
  background: #ff4d4f;
}

.tip-fade-enter-active,
.tip-fade-leave-active {
  transition: opacity 0.3s ease;
}

.tip-fade-enter-from,
.tip-fade-leave-to {
  opacity: 0;
}

.captcha-slider {
  width: 100%;
  height: 40px;
  position: relative;
  background: #f7f8fa;
  border-radius: 20px;
  overflow: hidden;
  margin-top: 10px;
  box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.08);
}

.slider-track {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 20px;
  overflow: hidden;
}

.slider-track-bg {
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, #f5f7fa, #e9ecef);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #e2e8f0;
  border-radius: 20px;
}

.slider-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(90deg, #4f46e5, #6366f1);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.slider-text {
  font-size: 14px;
  color: #999999;
  user-select: none;
  pointer-events: none;
}

.slider-fill .slider-text {
  color: #ffffff;
}

.slider-button {
  position: absolute;
  top: 0;
  left: 0;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border: 1px solid #e2e8f0;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12), 0 2px 8px rgba(0, 0, 0, 0.08);
  z-index: 2;
  user-select: none;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.slider-button:active {
  transform: scale(0.95);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
}

.slider-icon {
  font-size: 16px;
  transition: all 0.3s ease;
}

.icon-normal {
  color: #999999;
}

.icon-success {
  color: #4f46e5;
}

/* uni-app特定样式（通过CSS变量控制） */
.slide-captcha[data-env="uniapp"] .captcha-status {
  padding: 32rpx 40rpx;
  margin-bottom: 40rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
}

.slide-captcha[data-env="uniapp"] .status-icon {
  font-size: 48rpx;
  margin-right: 24rpx;
}

.slide-captcha[data-env="uniapp"] .status-text {
  font-size: 48rpx;
}

.slide-captcha[data-env="uniapp"] .inline-captcha {
  margin-top: 40rpx;
}

.slide-captcha[data-env="uniapp"] .captcha-image-panel {
  margin-bottom: 40rpx;
  border-radius: 48rpx;
  border: 4rpx solid #e2e8f0;
}

.slide-captcha[data-env="uniapp"] .captcha-target-hint {
  border: 8rpx dashed rgba(79, 70, 229, 0.3);
  border-radius: 32rpx;
}

.slide-captcha[data-env="uniapp"] .captcha-block {
  border-radius: 32rpx;
}

.slide-captcha[data-env="uniapp"] .captcha-block-image {
  border-radius: 32rpx;
}

.slide-captcha[data-env="uniapp"] .captcha-refresh {
  top: 16rpx;
  right: 16rpx;
  width: 60rpx !important;
  height: 60rpx !important;
}

.slide-captcha[data-env="uniapp"] .refresh-icon {
  font-size: 48rpx;
}

.slide-captcha[data-env="uniapp"] .captcha-tip {
  padding: 16rpx 32rpx;
  border-radius: 12rpx;
  font-size: 48rpx;
}

.slide-captcha[data-env="uniapp"] .captcha-slider {
  height: 80rpx;
  border-radius: 40rpx;
  margin-top: 20rpx;
}

.slide-captcha[data-env="uniapp"] .slider-track {
  border-radius: 40rpx;
}

.slide-captcha[data-env="uniapp"] .slider-track-bg {
  border: 4rpx solid #e2e8f0;
  border-radius: 40rpx;
}

.slide-captcha[data-env="uniapp"] .slider-fill {
  border-radius: 40rpx;
}

.slide-captcha[data-env="uniapp"] .slider-text {
  font-size: 56rpx;
}

.slide-captcha[data-env="uniapp"] .slider-button {
  width: 80rpx;
  height: 80rpx;
  border: 4rpx solid #e2e8f0;
  border-radius: 40rpx;
}

.slide-captcha[data-env="uniapp"] .slider-icon {
  font-size: 64rpx;
}
</style>