<template>
  <div class="slide-captcha" data-env="admin">
    <!-- 验证码状态提示 -->
    <div class="captcha-status" v-if="verified">
      <span class="status-icon">✅</span>
      <span class="status-text">{{ config?.TEXTS?.VERIFIED_STATUS || '滑动验证码已通过' }}</span>
    </div>
    <div class="captcha-status warning" v-else>
      <span class="status-icon">🔒</span>
      <span class="status-text">{{ config?.TEXTS?.UNVERIFIED_STATUS || '请完成下方滑动验证码' }}</span>
    </div>

    <!-- 内嵌滑动验证码 -->
    <div v-if="!verified && captchaData?.originalImageBase64" class="inline-captcha">
      <!-- 背景图片容器 -->
      <div
        class="captcha-image-panel"
        :style="getImagePanelStyle()"
      >
        <!-- 背景图片 -->
        <img
          :src="'data:image/png;base64,' + (captchaData?.originalImageBase64 || '')"
          class="captcha-bg-image"
          draggable="false"
        />

        <!-- 目标区域提示 (仅在未验证时显示) -->
        <div
          v-if="captchaData?.jigsawImageBase64 && !verifySuccess"
          class="captcha-target-hint"
          :style="getTargetHintStyle()"
        />

        <!-- 滑块图片 -->
        <div
          v-if="captchaData?.jigsawImageBase64"
          class="captcha-block"
          :style="getBlockStyle()"
        >
          <img
            :src="'data:image/png;base64,' + (captchaData?.jigsawImageBase64 || '')"
            class="captcha-block-image"
            draggable="false"
          />
        </div>

        <!-- 刷新按钮 -->
        <div
          class="captcha-refresh"
          @click="refreshCaptcha"
        >
          <span class="refresh-icon">🔄</span>
        </div>



        <!-- 提示信息 -->
        <transition name="tip-fade">
          <div v-if="tipMessage" class="captcha-tip" :class="verifySuccess ? 'tip-success' : 'tip-error'">
            {{ tipMessage }}
          </div>
        </transition>
      </div>

      <!-- 滑动条 -->
      <div class="captcha-slider" :style="getSliderStyle()">
        <!-- 滑动轨道 -->
        <div class="slider-track">
          <div class="slider-track-bg">
            <span class="slider-text">{{ sliderText }}</span>
          </div>

          <!-- 已滑动区域 -->
          <div
            class="slider-fill"
            :style="getSliderFillStyle()"
          >
            <span class="slider-text">{{ finishText }}</span>
          </div>
        </div>

        <!-- 滑块 -->
        <div
          class="slider-button"
          :style="getSliderButtonStyle()"
          @mousedown="handleMouseDown"
          @touchstart="handleTouchStart"
        >
          <span class="slider-icon" :class="verifySuccess ? 'icon-success' : 'icon-normal'">
            {{ verifySuccess ? '✓' : '➤' }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { CAPTCHA_CONFIG, formatVerifyData } from './captcha-config.js'

// Props
const props = defineProps({
  getCaptcha: {
    type: Function,
    required: true
  },
  checkCaptcha: {
    type: Function,
    required: true
  }
})

// Emits
const emit = defineEmits(['verified', 'error'])

// 配置
const config = ref(CAPTCHA_CONFIG)

// 验证码相关状态
const captchaToken = ref('')
const captchaVerification = ref('')
const verified = ref(false)

// 验证码数据
const captchaData = reactive({
  token: '',
  originalImageBase64: '',
  jigsawImageBase64: '',
  secretKey: '',
  result: false
})

// 图片尺寸配置（admin环境使用px）
const imgWidth = ref(CAPTCHA_CONFIG.IMAGE_WIDTH)
const imgHeight = ref(CAPTCHA_CONFIG.IMAGE_HEIGHT)
const blockWidth = ref(CAPTCHA_CONFIG.BLOCK_WIDTH)

// 滑块状态
const sliderLeft = ref(0)
const blockLeft = ref(0)
const isMoving = ref(false)
const startX = ref(0)
const verifySuccess = ref(false)

// 文本提示
const sliderText = ref(CAPTCHA_CONFIG.TEXTS.SLIDE_TEXT)
const finishText = ref(CAPTCHA_CONFIG.TEXTS.SUCCESS_TEXT)
const tipMessage = ref('')

// 样式计算方法
const getUnit = (value) => value + 'px'

const getImagePanelStyle = () => ({
  width: getUnit(imgWidth.value),
  height: getUnit(imgHeight.value)
})

const getTargetHintStyle = () => ({
  left: getUnit(Math.round((imgWidth.value - blockWidth.value) * 0.75)),
  top: getUnit(20),
  width: getUnit(blockWidth.value),
  height: getUnit(imgHeight.value - 40)
})

const getBlockStyle = () => ({
  left: getUnit(blockLeft.value),
  top: getUnit(0),
  width: getUnit(blockWidth.value),
  height: getUnit(imgHeight.value)
})

const getSliderStyle = () => ({
  width: getUnit(imgWidth.value),
  height: getUnit(40)
})

const getSliderFillStyle = () => ({
  width: getUnit(sliderLeft.value),
  transition: isMoving.value ? 'none' : 'all 0.3s ease'
})

const getSliderButtonStyle = () => ({
  left: getUnit(sliderLeft.value),
  width: getUnit(40),
  height: getUnit(40),
  transition: isMoving.value ? 'none' : 'all 0.3s ease'
})

// 初始化验证码
const initCaptcha = async () => {
  console.log('🚀 开始初始化验证码... (Vue3 Admin)')
  
  try {
    const response = await props.getCaptcha()
    const captchaDataResponse = response?.data?.data || response?.data
    
    if (captchaDataResponse && captchaDataResponse.originalImageBase64) {
      console.log('✅ 验证码数据加载成功:', {
        token: captchaDataResponse.token,
        hasOriginalImage: !!captchaDataResponse.originalImageBase64,
        hasJigsawImage: !!captchaDataResponse.jigsawImageBase64
      })
      
      Object.assign(captchaData, captchaDataResponse)
      resetSlider()
    } else {
      throw new Error('验证码数据格式错误')
    }
  } catch (error) {
    console.error('❌ 获取验证码失败:', error)
    showTip('验证码加载失败', false)
    emit('error', error.message || '验证码加载失败')
  }
}

// 刷新验证码
const refreshCaptcha = async () => {
  resetCaptchaState()
  await initCaptcha()
}

// 重置验证码状态
const resetCaptchaState = () => {
  Object.assign(captchaData, {
    token: '',
    originalImageBase64: '',
    jigsawImageBase64: '',
    secretKey: '',
    result: false
  })
  resetSlider()
  tipMessage.value = ''
  verifySuccess.value = false
  verified.value = false
}

// 重置滑块状态
const resetSlider = () => {
  sliderLeft.value = 0
  blockLeft.value = 0
  isMoving.value = false
  verifySuccess.value = false
}

// 更新滑块位置
const updateSliderPosition = (deltaX) => {
  const maxSlideDistance = imgWidth.value - 40 // 40px是滑块按钮宽度
  const newSliderLeft = Math.max(0, Math.min(deltaX, maxSlideDistance))

  console.log('📍 更新滑块位置:', {
    deltaX,
    maxSlideDistance,
    newSliderLeft,
    imgWidth: imgWidth.value
  })

  sliderLeft.value = newSliderLeft
  blockLeft.value = newSliderLeft
}

// 验证验证码位置
const verifyCaptchaPosition = async () => {
  try {
    const pixelX = Math.round(blockLeft.value)

    const verifyData = formatVerifyData(
      captchaData.token,
      pixelX,
      captchaData.secretKey
    )

    console.log('🔍 验证数据:', { pixelX, verifyData })

    const response = await props.checkCaptcha(verifyData)

    // 正确解析axios响应格式
    // axios返回: { data: { success: boolean, data: { result: boolean }, message: string } }
    const apiResponse = response.data
    const isSuccess = apiResponse?.success && apiResponse?.data?.result

    console.log('📋 验证响应:', {
      fullResponse: response.data,
      success: apiResponse?.success,
      result: apiResponse?.data?.result,
      message: apiResponse?.message
    })

    if (isSuccess) {
      verifySuccess.value = true
      verified.value = true
      captchaToken.value = captchaData.token
      captchaVerification.value = captchaData.secretKey
      showTip('验证成功', true)

      console.log('✅ 验证成功:', {
        token: captchaToken.value,
        verification: captchaVerification.value
      })

      emit('verified', {
        token: captchaToken.value,
        verification: captchaVerification.value
      })
    } else {
      console.log('❌ 验证失败:', apiResponse?.message || '位置不正确')
      showTip(apiResponse?.message || '验证失败，请重试', false)
      resetSlider()
    }
  } catch (error) {
    console.error('❌ 验证过程出错:', error)
    showTip('验证失败，请重试', false)
    resetSlider()
    emit('error', error.message || '验证失败，请重试')
  }
}

// 显示提示信息
const showTip = (message, isSuccess) => {
  tipMessage.value = message
  verifySuccess.value = isSuccess
  
  console.log('💬 显示提示:', { message, isSuccess })
  
  setTimeout(() => {
    if (!isSuccess) {
      tipMessage.value = ''
    }
  }, 3000)
}

// 鼠标事件处理
const handleMouseDown = (e) => {
  if (verifySuccess.value) return
  
  isMoving.value = true
  startX.value = e.clientX
  tipMessage.value = ''
  
  console.log('🖱️ 鼠标按下:', { startX: startX.value })
  
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
  e.preventDefault()
}

const handleMouseMove = (e) => {
  if (!isMoving.value || verifySuccess.value) return

  const deltaX = e.clientX - startX.value
  console.log('🖱️ 鼠标移动:', { clientX: e.clientX, startX: startX.value, deltaX })
  updateSliderPosition(deltaX)
}

const handleMouseUp = async () => {
  if (!isMoving.value || verifySuccess.value) return
  
  isMoving.value = false
  console.log('🖱️ 鼠标松开')
  
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
  
  await verifyCaptchaPosition()
}

// 触摸事件处理
const handleTouchStart = (e) => {
  if (verifySuccess.value) return
  
  isMoving.value = true
  startX.value = e.touches[0].clientX
  tipMessage.value = ''
  
  console.log('👆 触摸开始:', { startX: startX.value })
  
  document.addEventListener('touchmove', handleTouchMove, { passive: false })
  document.addEventListener('touchend', handleTouchEnd)
  e.preventDefault()
}

const handleTouchMove = (e) => {
  if (!isMoving.value || verifySuccess.value) return
  
  const deltaX = e.touches[0].clientX - startX.value
  updateSliderPosition(deltaX)
  e.preventDefault()
}

const handleTouchEnd = async () => {
  if (!isMoving.value || verifySuccess.value) return
  
  isMoving.value = false
  console.log('👆 触摸结束')
  
  document.removeEventListener('touchmove', handleTouchMove)
  document.removeEventListener('touchend', handleTouchEnd)
  
  await verifyCaptchaPosition()
}

// 组件挂载后初始化验证码
onMounted(() => {
  initCaptcha()
})



// 暴露方法给父组件
defineExpose({
  reset: () => {
    resetCaptchaState()
    initCaptcha()
  },
  getVerificationData: () => ({
    verified: verified.value,
    token: captchaToken.value,
    verification: captchaVerification.value
  }),
  testDrag: testDragToDistance
})
</script>

<style scoped>
/* 滑动验证码容器 */
.slide-captcha {
  width: 100%;
  margin: 16px 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 验证码状态提示 */
.captcha-status {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 14px;
  margin-bottom: 12px;
  background: #f0f9ff;
  border: 1px solid #0ea5e9;
  color: #0369a1;
}

.captcha-status.warning {
  background: #fef3c7;
  border-color: #f59e0b;
  color: #92400e;
}

.status-icon {
  font-size: 16px;
}

.status-text {
  font-weight: 500;
}

/* 内嵌验证码容器 */
.inline-captcha {
  width: 100%;
}

/* 图片面板 */
.captcha-image-panel {
  position: relative;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  background: #f9fafb;
  margin-bottom: 8px;
}

/* 背景图片 */
.captcha-bg-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  user-select: none;
  -webkit-user-select: none;
}

/* 目标区域提示 */
.captcha-target-hint {
  position: absolute;
  background: rgba(59, 130, 246, 0.2);
  border: 2px dashed #3b82f6;
  border-radius: 4px;
  pointer-events: none;
  animation: hint-pulse 2s infinite;
}

@keyframes hint-pulse {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.6; }
}

/* 滑块图片 */
.captcha-block {
  position: absolute;
  z-index: 10;
  cursor: grab;
  transition: all 0.3s ease;
}

.captcha-block:active {
  cursor: grabbing;
}

.captcha-block-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  user-select: none;
  -webkit-user-select: none;
  filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.3));
}

/* 刷新按钮 */
.captcha-refresh {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #d1d5db;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 20;
}

.captcha-refresh:hover {
  background: #ffffff;
  border-color: #9ca3af;
  transform: scale(1.05);
}

.refresh-icon {
  font-size: 14px;
  transition: transform 0.3s ease;
}

.captcha-refresh:hover .refresh-icon {
  transform: rotate(180deg);
}

/* 提示信息 */
.captcha-tip {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  z-index: 30;
  white-space: nowrap;
  pointer-events: none;
}

.tip-success {
  background: #dcfce7;
  color: #166534;
  border: 1px solid #22c55e;
}

.tip-error {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #ef4444;
}

/* 提示动画 */
.tip-fade-enter-active, .tip-fade-leave-active {
  transition: all 0.3s ease;
}

.tip-fade-enter-from, .tip-fade-leave-to {
  opacity: 0;
  transform: translate(-50%, -50%) scale(0.8);
}

/* 滑动条 */
.captcha-slider {
  position: relative;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  overflow: hidden;
}

/* 滑动轨道 */
.slider-track {
  position: relative;
  width: 100%;
  height: 100%;
}

.slider-track-bg {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(90deg, #f3f4f6 0%, #e5e7eb 100%);
}

/* 已滑动区域 */
.slider-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

/* 滑动文本 */
.slider-text {
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  user-select: none;
  -webkit-user-select: none;
  pointer-events: none;
}

.slider-fill .slider-text {
  color: #ffffff;
}

/* 滑块按钮 */
.slider-button {
  position: absolute;
  top: 0;
  background: #ffffff;
  border: 2px solid #d1d5db;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: grab;
  z-index: 10;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.slider-button:active {
  cursor: grabbing;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.slider-button:hover {
  border-color: #9ca3af;
  background: #f9fafb;
}

/* 滑块图标 */
.slider-icon {
  font-size: 16px;
  font-weight: bold;
  transition: all 0.2s ease;
  user-select: none;
  -webkit-user-select: none;
}

.icon-normal {
  color: #6b7280;
}

.icon-success {
  color: #22c55e;
  transform: scale(1.1);
}

/* 响应式设计 */
@media (max-width: 640px) {
  .slide-captcha {
    margin: 12px 0;
  }

  .captcha-status {
    font-size: 13px;
    padding: 6px 10px;
  }

  .captcha-refresh {
    width: 28px;
    height: 28px;
    top: 6px;
    right: 6px;
  }

  .refresh-icon {
    font-size: 12px;
  }

  .captcha-tip {
    font-size: 13px;
    padding: 6px 12px;
  }

  .slider-text {
    font-size: 13px;
  }

  .slider-icon {
    font-size: 14px;
  }
}

/* 无障碍支持 */
@media (prefers-reduced-motion: reduce) {
  .captcha-target-hint {
    animation: none;
  }

  .tip-fade-enter-active, .tip-fade-leave-active {
    transition: none;
  }

  .slider-fill, .slider-button {
    transition: none;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .captcha-image-panel {
    border-color: #000000;
    border-width: 3px;
  }

  .captcha-status {
    border-width: 2px;
  }

  .slider-button {
    border-color: #000000;
    border-width: 3px;
  }
}
</style>
